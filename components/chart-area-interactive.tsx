"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

import { useIsMobile } from "@/hooks/use-mobile"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group"

export const description = "An interactive area chart"

// Combined data from Instruments Analysis and Real Estate Analysis
const chartData = [
  { date: "2005-01-01", instruments: 1000000.00, realEstate: 0 },
  { date: "2019-01-01", instruments: 0, realEstate: 2347700.08 },
  { date: "2020-01-01", instruments: 1900000.00, realEstate: 0 },
  { date: "2021-01-01", instruments: 4674334.69, realEstate: 1000000.0 },
  { date: "2022-01-01", instruments: 500000.00, realEstate: 3848669.37 },
  { date: "2024-01-01", instruments: 2587167.33, realEstate: 0 },
]

const chartConfig = {
  investments: {
    label: "Total Investments",
  },
  instruments: {
    label: "Instruments Analysis",
    color: "var(--primary)",
  },
  realEstate: {
    label: "Real Estate Analysis",
    color: "var(--primary)",
  },
} satisfies ChartConfig

export function ChartAreaInteractive() {
  const isMobile = useIsMobile()
  const [timeRange, setTimeRange] = React.useState("all")

  React.useEffect(() => {
    if (isMobile) {
      setTimeRange("recent")
    }
  }, [isMobile])

  const filteredData = React.useMemo(() => {
    if (timeRange === "recent") {
      // Show only 2020 onwards for mobile
      return chartData.filter((item) => {
        const year = new Date(item.date).getFullYear()
        return year >= 2020
      })
    } else if (timeRange === "latest") {
      // Show only 2021 onwards
      return chartData.filter((item) => {
        const year = new Date(item.date).getFullYear()
        return year >= 2021
      })
    }
    // Show all data
    return chartData
  }, [timeRange])



  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Total Investments</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">
            Instruments Analysis & Real Estate Analysis commitments by year
          </span>
          <span className="@[540px]/card:hidden">Investment commitments</span>
        </CardDescription>
        <CardAction>
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
          >
            <ToggleGroupItem value="all">All Years</ToggleGroupItem>
            <ToggleGroupItem value="recent">2020+</ToggleGroupItem>
            <ToggleGroupItem value="latest">2021+</ToggleGroupItem>
          </ToggleGroup>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
              size="sm"
              aria-label="Select a value"
            >
              <SelectValue placeholder="All Years" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="all" className="rounded-lg">
                All Years
              </SelectItem>
              <SelectItem value="recent" className="rounded-lg">
                2020+
              </SelectItem>
              <SelectItem value="latest" className="rounded-lg">
                2021+
              </SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillInstruments" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-instruments)"
                  stopOpacity={1.0}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-instruments)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillRealEstate" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-realEstate)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-realEstate)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.getFullYear().toString()
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : 2}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return `Year ${new Date(value).getFullYear()}`
                  }}
                  formatter={(value, name) => [
                    new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: "USD",
                    }).format(Number(value)),
                    name === "instruments" ? " Instruments Analysis " : " Real Estate Analysis "
                  ]}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="realEstate"
              type="natural"
              fill="url(#fillRealEstate)"
              stroke="var(--color-realEstate)"
              stackId="a"
            />
            <Area
              dataKey="instruments"
              type="natural"
              fill="url(#fillInstruments)"
              stroke="var(--color-instruments)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
