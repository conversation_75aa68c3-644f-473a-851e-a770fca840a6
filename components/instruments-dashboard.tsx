"use client";

import React from "react";
import { Icon<PERSON><PERSON><PERSON>, IconTrendingUp } from "@tabler/icons-react";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";

// Helper functions
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value);
};

const formatCurrencyShort = (value: number): string => {
  if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(0)}K`;
  }
  return formatCurrency(value);
};

// Data structure based on code(10).html
const instrumentsData = {
  totalOutstanding: 3644435.50,
  pairedCommitments: [
    {
      year: "2005",
      totalCommitment: 1000000.00,
      subtotal: 618545.83,
      instruments: [
        {
          name: "ISF III 2005",
          assetClass: "Venture Capital",
          commitment: 1000000.00,
          called: 381454.17,
          outstanding: 618545.83,
        },
      ],
    },
    {
      year: "2020",
      totalCommitment: 1900000.00,
      subtotal: 713520.62,
      instruments: [
        {
          name: "TARGET ISRAELI BUYOUT 2020 LP CLASS A",
          assetClass: "Private Markets",
          commitment: 900000.00,
          called: 441000.00,
          outstanding: 459000.00,
        },
        {
          name: "SLA II / Silver Lake II",
          assetClass: "Private Markets",
          commitment: 1000000.00,
          called: 745479.38,
          outstanding: 254520.62,
        },
      ],
    },
    {
      year: "2021",
      totalCommitment: 4674334.69,
      subtotal: 679130.63,
      instruments: [
        {
          name: "FOCUS TECH 2021 LP CLASS A SHARES",
          assetClass: "Private Markets",
          commitment: 1000000.00,
          called: 950000.00,
          outstanding: 50000.00,
        },
        {
          name: "Frux Debt Fund II 2021",
          assetClass: "Private Markets",
          commitment: 1174334.69,
          called: 1120583.84,
          outstanding: 53750.85,
        },
        {
          name: "HarbourVest Co-Investment VI 2021",
          assetClass: "Private Markets",
          commitment: 1000000.00,
          called: 839976.73,
          outstanding: 160023.27,
        },
        {
          name: "Harbourvest Global Access 2021",
          assetClass: "Private Markets",
          commitment: 1000000.00,
          called: 674019.58,
          outstanding: 325980.42,
        },
        {
          name: "Tiger Global XV Private Investors (bankable) 2021",
          assetClass: "Private Markets",
          commitment: 500000.00,
          called: 475025.73,
          outstanding: 24974.27,
        },
        {
          name: "GA 2021 PRIVATE INVESTORS CLASS A 2021",
          assetClass: "Venture Capital",
          commitment: 1000000.00,
          called: 935598.18,
          outstanding: 64401.82,
        },
      ],
    },
    {
      year: "2022",
      totalCommitment: 500000.00,
      subtotal: 255385.05,
      instruments: [
        {
          name: "Lexington X Private Investors 2022",
          assetClass: "Venture Capital",
          commitment: 500000.00,
          called: 244614.95,
          outstanding: 255385.05,
        },
      ],
    },
    {
      year: "2024",
      totalCommitment: 2587167.33,
      subtotal: 1283353.37,
      instruments: [
        {
          name: "Frux Fund III SCSp RAIF 2024",
          assetClass: "Private Markets",
          commitment: 587167.33,
          called: 472669.71,
          outstanding: 114497.62,
        },
        {
          name: "LTO 4 2024",
          assetClass: "Private Markets",
          commitment: 2000000.00,
          called: 831144.25,
          outstanding: 1168855.75,
        },
      ],
    },
  ],
  anomalies: [
    {
      name: "CREDIT SUISSE PRIVATE EQUITY PLATFORM",
      assetClass: "Private Markets",
      outstanding: 94500.00,
    },
  ],
};

// Chart data and config
const chartData = [
  { year: "2005", commitment: 1000000.00 },
  { year: "2020", commitment: 1900000.00 },
  { year: "2021", commitment: 4674334.69 },
  { year: "2022", commitment: 500000.00 },
  { year: "2024", commitment: 2587167.33 },
  { year: "Unspecified", commitment: 350000.00 },
];

const chartConfig = {
  commitment: {
    label: "Commitment",
    color: "var(--primary)",
  },
} satisfies ChartConfig;

export function InstrumentsDashboard() {
  const handlePrint = () => {
    // Add print styles dynamically
    const printStyles = `
      <style>
        @media print {
          @page {
            size: A4;
            margin: 1.5cm;
          }

          body {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          /* Hide sidebar and navigation */
          [data-sidebar] {
            display: none !important;
          }

          header {
            display: none !important;
          }

          /* Main content adjustments */
          main {
            margin: 0 !important;
            padding: 0 !important;
            max-width: 100% !important;
          }

          /* Card styling for print */
          [data-card] {
            border: 1px solid #e5e7eb !important;
            box-shadow: none !important;
            margin-bottom: 1rem !important;
            page-break-inside: avoid;
          }

          /* Summary cards layout */
          .grid {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 1rem !important;
            margin-bottom: 2rem !important;
          }

          /* Chart container */
          .recharts-wrapper {
            page-break-inside: avoid;
          }

          /* Table styling */
          table {
            width: 100% !important;
            border-collapse: collapse !important;
            font-size: 10pt !important;
          }

          th, td {
            border: 1px solid #d1d5db !important;
            padding: 6px 8px !important;
            text-align: left !important;
          }

          th {
            background-color: #f3f4f6 !important;
            font-weight: bold !important;
          }

          /* Year headers */
          tr[class*="bg-muted"] td {
            background-color: #f9fafb !important;
            font-weight: bold !important;
          }

          /* Outstanding amounts */
          .font-semibold {
            font-weight: bold !important;
          }

          /* Page breaks */
          .px-4 {
            page-break-inside: avoid;
            margin-bottom: 1.5rem !important;
          }

          /* Hide unnecessary elements */
          button {
            display: none !important;
          }

          /* Title styling */
          h1, h2, h3 {
            color: #111827 !important;
            margin-bottom: 1rem !important;
          }

          /* Ensure text is visible */
          * {
            color: #111827 !important;
          }

          .text-muted-foreground {
            color: #6b7280 !important;
          }
        }
      </style>
    `;

    // Add styles to head
    const styleElement = document.createElement('div');
    styleElement.innerHTML = printStyles;
    const styleNode = styleElement.firstChild;
    if (styleNode) {
      document.head.appendChild(styleNode);
    }

    // Print after a short delay to ensure styles are applied
    setTimeout(() => {
      window.print();
      // Clean up styles after printing
      setTimeout(() => {
        const addedStyle = document.head.querySelector('style:last-child');
        if (addedStyle) {
          document.head.removeChild(addedStyle);
        }
      }, 1000);
    }, 100);
  };

  // Calculate totals
  const totalCommitted = instrumentsData.pairedCommitments.reduce(
    (sum, year) => sum + year.totalCommitment, 0
  ) + 350000.00; // Add unspecified year

  const totalCalled = instrumentsData.pairedCommitments.reduce(
    (sum, year) => sum + year.instruments.reduce(
      (instrumentSum, instrument) => instrumentSum + instrument.called, 0
    ), 0
  ) + 255500.00; // Add called amount for unspecified year

  const totalInstruments = instrumentsData.pairedCommitments.reduce(
    (sum, year) => sum + year.instruments.length, 0
  ) + instrumentsData.anomalies.length;

  return (
    <div className="flex flex-1 flex-col">
      {/* Print-only header */}
      <div className="hidden print:block print:mb-6">
        <h1 className="text-2xl font-bold text-center mb-2">All Other Commitments Analysis</h1>
        <p className="text-center text-muted-foreground">Detailed Breakdown of Private Markets, Venture Capital, and Other Commitments</p>
        <p className="text-center text-sm text-muted-foreground mt-2">
          Data as of {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
        </p>
      </div>

      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Summary Cards */}
          <div className="grid gap-4 px-4 lg:px-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Outstanding
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(instrumentsData.totalOutstanding)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Non-Real Estate Commitments
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Committed
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(totalCommitted)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all years
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Called
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(totalCalled)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Capital deployed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Instruments
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalInstruments}</div>
                <p className="text-xs text-muted-foreground">
                  Including anomalies
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Chart Section */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Commitments by Year</CardTitle>
                <CardDescription>
                  Total commitment amounts for Private Markets & Venture Capital
                </CardDescription>
                <CardAction>
                  <Button variant="outline" size="sm" onClick={handlePrint}>
                    <IconPrinter className="h-4 w-4" />
                    Export
                  </Button>
                </CardAction>
              </CardHeader>
              <CardContent className="px-2 sm:p-6">
                <ChartContainer
                  config={chartConfig}
                  className="aspect-auto h-[250px] w-full"
                >
                  <BarChart
                    accessibilityLayer
                    data={chartData}
                    margin={{
                      left: 12,
                      right: 12,
                    }}
                  >
                    <CartesianGrid vertical={false} />
                    <XAxis
                      dataKey="year"
                      tickLine={false}
                      axisLine={false}
                      tickMargin={8}
                      minTickGap={32}
                    />
                    <ChartTooltip
                      content={
                        <ChartTooltipContent
                          className="w-[150px]"
                          nameKey="commitment"
                          labelFormatter={(value) => `Year ${value}`}
                          formatter={(value) => [formatCurrency(Number(value)), "Commitment"]}
                        />
                      }
                    />
                    <Bar
                      dataKey="commitment"
                      fill="var(--color-commitment)"
                      radius={4}
                    />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Paired Commitments Table */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Paired Commitments</CardTitle>
                <CardDescription>
                  Detailed breakdown of committed, called, and outstanding capital
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Instrument</TableHead>
                      <TableHead>Asset Class</TableHead>
                      <TableHead className="text-right">Commitment</TableHead>
                      <TableHead className="text-right">Called</TableHead>
                      <TableHead className="text-right">Outstanding</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {instrumentsData.pairedCommitments.map((yearGroup) => (
                      <React.Fragment key={yearGroup.year}>
                        {/* Year Header */}
                        <TableRow className="bg-muted/50">
                          <TableCell colSpan={3} className="font-semibold">
                            {yearGroup.year} Commitments
                          </TableCell>
                          <TableCell className="text-right text-muted-foreground text-sm">
                            Total:
                          </TableCell>
                          <TableCell className="text-right font-medium">
                            {formatCurrency(yearGroup.totalCommitment)}
                          </TableCell>
                        </TableRow>

                        {/* Instruments */}
                        {yearGroup.instruments.map((instrument, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {instrument.name}
                            </TableCell>
                            <TableCell className="text-muted-foreground">
                              {instrument.assetClass}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              {formatCurrency(instrument.commitment)}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              {formatCurrency(instrument.called)}
                            </TableCell>
                            <TableCell className="text-right font-mono font-semibold">
                              {formatCurrency(instrument.outstanding)}
                            </TableCell>
                          </TableRow>
                        ))}

                        {/* Subtotal */}
                        <TableRow className="bg-muted/30">
                          <TableCell colSpan={4} className="text-right font-medium text-muted-foreground">
                            {yearGroup.year} Outstanding Subtotal:
                          </TableCell>
                          <TableCell className="text-right font-bold">
                            {formatCurrency(yearGroup.subtotal)}
                          </TableCell>
                        </TableRow>
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>

          {/* Anomalies Table */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Anomalies: Unpaired Positions</CardTitle>
                <CardDescription>
                  Instruments with commitment-only positions requiring attention
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Instrument (Commitment Only)</TableHead>
                      <TableHead>Asset Class</TableHead>
                      <TableHead className="text-right">Outstanding Commitment</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {instrumentsData.anomalies.map((anomaly, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{anomaly.name}</TableCell>
                        <TableCell className="text-muted-foreground">{anomaly.assetClass}</TableCell>
                        <TableCell className="text-right font-mono font-semibold">
                          {formatCurrency(anomaly.outstanding)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
