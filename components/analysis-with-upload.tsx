"use client";

import React, { useState } from "react";
import { CombinedAnalysisDashboard } from "@/components/combined-analysis-dashboard";
import { FileUpload } from "@/components/file-upload";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { IconUpload, IconDatabase, IconRefresh, IconAlertCircle, IconCheck } from "@tabler/icons-react";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useInvestmentData, useMongoConnection } from "@/hooks/use-investment-data";

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  content: any;
  uploadedAt: Date;
}

interface ProcessedInstrument {
  id: number;
  instrument: string;
  assetClass: string;
  calledAmount: number;
  commitmentAmount: number;
  status: "Paired" | "Unpaired";
  missingType?: "Called" | "Commitment";
}

interface RawDataEntry {
  // Support both formats - your actual data format and the expected format
  instrument?: string;
  Instrument?: string;
  assetClass?: string;
  "Asset Class"?: string;
  outstanding?: number;
  "Value WA Portfolio CCY"?: string | number;
  "Value Portfolio CCY"?: string | number;
  "Capital Commitment"?: string;
  "Paid Commitment"?: string;
  "Outstanding Commitment"?: string;
  [key: string]: any;
}

// Function to process uploaded data and find paired/unpaired instruments
function processInstrumentData(rawData: RawDataEntry[]): {
  pairedInstruments: ProcessedInstrument[];
  unpairedInstruments: ProcessedInstrument[];
} {
  const processedInstruments: ProcessedInstrument[] = [];
  let id = 1;

  // Check if data has the expected "- Called" / "- Commitment" format
  const hasCallCommitmentFormat = rawData.some(entry => {
    const instrumentName = entry.instrument || entry.Instrument || "";
    return instrumentName.includes("- Called") || instrumentName.includes("- Commitment");
  });

  if (hasCallCommitmentFormat) {
    // Use the original pairing logic for data with "- Called" / "- Commitment" format
    const instrumentMap = new Map<string, { called?: RawDataEntry; commitment?: RawDataEntry }>();

    rawData.forEach((entry) => {
      const instrumentField = entry.instrument || entry.Instrument;
      if (!instrumentField || typeof instrumentField !== 'string' || !instrumentField.trim()) {
        console.warn("Skipping invalid entry:", entry);
        return;
      }

      let instrumentName = instrumentField.trim();
      let status: "Called" | "Commitment" | null = null;

      if (instrumentName.includes("- Called")) {
        instrumentName = instrumentName.replace("- Called", "").trim();
        status = "Called";
      } else if (instrumentName.includes("- Commitment")) {
        instrumentName = instrumentName.replace("- Commitment", "").trim();
        status = "Commitment";
      }

      if (!status || !instrumentName) return;

      if (!instrumentMap.has(instrumentName)) {
        instrumentMap.set(instrumentName, {});
      }

      const instrumentEntry = instrumentMap.get(instrumentName)!;
      if (status === "Called") {
        instrumentEntry.called = entry;
      } else {
        instrumentEntry.commitment = entry;
      }
    });

    const pairedInstruments: ProcessedInstrument[] = [];
    const unpairedInstruments: ProcessedInstrument[] = [];

    instrumentMap.forEach((data, instrumentName) => {
      const hasCall = !!data.called;
      const hasCommitment = !!data.commitment;
      const assetClass = (data.commitment?.assetClass || data.commitment?.["Asset Class"] || data.called?.assetClass || data.called?.["Asset Class"] || "Unknown").toString();

      const processedInstrument: ProcessedInstrument = {
        id: id++,
        instrument: instrumentName,
        assetClass,
        calledAmount: data.called?.outstanding || 0,
        commitmentAmount: data.commitment?.outstanding || 0,
        status: hasCall && hasCommitment ? "Paired" : "Unpaired",
        missingType: !hasCall ? "Called" : !hasCommitment ? "Commitment" : undefined,
      };

      if (hasCall && hasCommitment) {
        pairedInstruments.push(processedInstrument);
      } else {
        unpairedInstruments.push(processedInstrument);
      }
    });

    return { pairedInstruments, unpairedInstruments };
  } else {
    // Handle your actual data format (without "- Called" / "- Commitment" suffixes)
    rawData.forEach((entry) => {
      const instrumentName = (entry.instrument || entry.Instrument || "").trim();
      if (!instrumentName) {
        console.warn("Skipping entry without instrument name:", entry);
        return;
      }

      const assetClass = (entry.assetClass || entry["Asset Class"] || "Unknown").toString();

      // Get value - try multiple possible fields
      let value = 0;
      if (entry.outstanding) {
        value = typeof entry.outstanding === 'number' ? entry.outstanding : parseFloat(entry.outstanding.toString()) || 0;
      } else if (entry["Value WA Portfolio CCY"]) {
        value = typeof entry["Value WA Portfolio CCY"] === 'number' ? entry["Value WA Portfolio CCY"] : parseFloat(entry["Value WA Portfolio CCY"].toString()) || 0;
      } else if (entry["Value Portfolio CCY"]) {
        value = typeof entry["Value Portfolio CCY"] === 'number' ? entry["Value Portfolio CCY"] : parseFloat(entry["Value Portfolio CCY"].toString()) || 0;
      }

      const processedInstrument: ProcessedInstrument = {
        id: id++,
        instrument: instrumentName,
        assetClass,
        calledAmount: 0, // Your data doesn't seem to have called amounts
        commitmentAmount: value,
        status: "Paired", // Treat all as paired since we don't have pairing logic
      };

      processedInstruments.push(processedInstrument);
    });

    return { 
      pairedInstruments: processedInstruments, 
      unpairedInstruments: [] 
    };
  }
}

export function AnalysisWithUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [useMongoData, setUseMongoData] = useState(true);
  const [processedData, setProcessedData] = useState<{
    pairedInstruments: ProcessedInstrument[];
    unpairedInstruments: ProcessedInstrument[];
  } | null>(null);

  // MongoDB hooks
  const {
    data: mongoData,
    summary,
    loading: dataLoading,
    error: dataError,
    refetch,
    uploadData
  } = useInvestmentData({ autoFetch: true });

  const {
    connected,
    loading: connectionLoading,
    error: connectionError,
    info: connectionInfo,
    testConnection
  } = useMongoConnection();

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(files);

    // If files are uploaded, try to use the first one for processing
    if (files.length > 0) {
      const firstFile = files[0];
      let rawData: RawDataEntry[] = [];

      // Extract data from different possible structures
      if (firstFile.content && Array.isArray(firstFile.content)) {
        rawData = firstFile.content;
      } else if (firstFile.content && firstFile.content.data && Array.isArray(firstFile.content.data)) {
        rawData = firstFile.content.data;
      }

      if (rawData.length > 0) {
        try {
          console.log("Processing raw data:", rawData.slice(0, 3)); // Log first 3 entries for debugging

          // Validate data structure - support both formats
          const validEntries = rawData.filter(entry => {
            if (!entry || typeof entry !== 'object') return false;

            // Check for instrument field (either lowercase or uppercase)
            const instrumentField = entry.instrument || entry.Instrument;
            return typeof instrumentField === 'string' && instrumentField.trim().length > 0;
          });

          if (validEntries.length === 0) {
            console.error("No valid entries found in uploaded data");
            console.error("Sample of invalid entries:", rawData.slice(0, 5));
            console.error("Expected format: Each entry should have an 'instrument' field as a non-empty string");

            // Check what fields are actually present
            if (rawData.length > 0) {
              const sampleEntry = rawData[0];
              console.error("First entry keys:", Object.keys(sampleEntry || {}));
              console.error("First entry:", sampleEntry);
            }
            return;
          }

          console.log(`Found ${validEntries.length} valid entries out of ${rawData.length} total entries`);

          // Process the data to find paired/unpaired instruments
          const processed = processInstrumentData(validEntries);
          setProcessedData(processed);
          setUseMongoData(false); // Switch to uploaded data

          console.log("Processed data:", {
            paired: processed.pairedInstruments.length,
            unpaired: processed.unpairedInstruments.length
          });

        } catch (error) {
          console.error("Error processing uploaded file:", error);
        }
      }
    }
  };

  const resetToDefaultData = () => {
    setUploadedFiles([]);
    setProcessedData(null);
    setUseMongoData(true);
  };

  return (
    <div className="flex flex-1 flex-col">
      {/* Upload Controls */}
      <div className="px-4 lg:px-6 mb-4">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <IconDatabase className="h-5 w-5" />
                  Data Source
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {!useMongoData && processedData ? "Using uploaded data" :
                   useMongoData && summary ? "Using MongoDB data" :
                   "No data available"}
                </p>
              </div>
              <div className="flex gap-2">
                {!useMongoData && processedData && (
                  <Button variant="outline" size="sm" onClick={resetToDefaultData}>
                    <IconRefresh className="h-4 w-4" />
                    Reset to MongoDB
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFileUpload(!showFileUpload)}
                >
                  <IconUpload className="h-4 w-4" />
                  Upload Data
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refetch}
                  disabled={dataLoading}
                >
                  <IconDatabase className="h-4 w-4" />
                  Refresh MongoDB
                </Button>
              </div>
            </div>
            
            {/* MongoDB Connection Status */}
            {useMongoData && (
              <div className="mt-3">
                {connectionLoading ? (
                  <Alert>
                    <IconDatabase className="h-4 w-4" />
                    <AlertDescription>Testing MongoDB connection...</AlertDescription>
                  </Alert>
                ) : connected ? (
                  <Alert>
                    <IconCheck className="h-4 w-4" />
                    <AlertDescription>
                      Connected to MongoDB: {connectionInfo?.database} ({connectionInfo?.investmentCount} records)
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert variant="destructive">
                    <IconAlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      MongoDB connection failed: {connectionError || 'Unknown error'}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {uploadedFiles.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {uploadedFiles.map((file) => (
                  <Badge key={file.id} variant="secondary">
                    {file.name} ({(file.size / 1024).toFixed(1)} KB)
                  </Badge>
                ))}
              </div>
            )}
          </CardHeader>
          
          {showFileUpload && (
            <CardContent>
              <FileUpload onFilesUploaded={handleFileUpload} />
            </CardContent>
          )}
        </Card>
      </div>

      {/* Combined Analysis Dashboard */}
      <CombinedAnalysisDashboard
        pairedInstruments={!useMongoData ? processedData?.pairedInstruments : undefined}
        unpairedInstruments={!useMongoData ? processedData?.unpairedInstruments : undefined}
        mongoSummary={useMongoData ? summary : undefined}
      />
    </div>
  );
}
