"use client";

import React, { useState, useMemo } from "react";
import { <PERSON>con<PERSON><PERSON><PERSON>, IconTrendingUp, IconFilter } from "@tabler/icons-react";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Bar, BarChart, CartesianGrid, XAxis } from "recharts";

// Helper functions
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value);
};

// No static data - using MongoDB or uploaded data only

type AssetClassFilter = "all" | "real-estate" | "other";

interface ExternalDataProps {
  pairedInstruments?: Array<{
    id: number;
    instrument: string;
    assetClass: string;
    calledAmount: number;
    commitmentAmount: number;
    status: "Paired" | "Unpaired";
    missingType?: "Called" | "Commitment";
  }>;
  unpairedInstruments?: Array<{
    id: number;
    instrument: string;
    assetClass: string;
    calledAmount: number;
    commitmentAmount: number;
    status: "Paired" | "Unpaired";
    missingType?: "Called" | "Commitment";
  }>;
  mongoSummary?: {
    totalOutstanding: number;
    totalCommitment: number;
    totalCalled: number;
    assetClasses: string[];
    yearlyBreakdown: Record<number, number>;
    statusBreakdown: Record<string, number>;
    pairedInstruments: Array<{
      name: string;
      assetClass: string;
      commitment: number;
      called: number;
      outstanding: number;
    }>;
    anomalies: Array<{
      name: string;
      assetClass: string;
      type: string;
      amount: number;
      details: string;
    }>;
    totalInstruments: number;
    pairedCount: number;
    anomalyCount: number;
  };
}

export function CombinedAnalysisDashboard({ pairedInstruments, unpairedInstruments, mongoSummary }: ExternalDataProps = {}) {
  const [assetClassFilter, setAssetClassFilter] = useState<AssetClassFilter>("all");

  // Use external data if provided, otherwise use default data
  const combinedData = useMemo(() => {
    if (mongoSummary) {
      // Convert MongoDB summary data to our internal format
      const pairedCommitments: Array<{
        year: string;
        totalCommitment: number;
        subtotal: number;
        instruments: Array<{
          name: string;
          assetClass: string;
          commitment: number;
          called: number;
          outstanding: number;
        }>;
      }> = [];

      // Group paired instruments by year (using current year as default)
      const yearMap = new Map<string, Array<{
        name: string;
        assetClass: string;
        commitment: number;
        called: number;
        outstanding: number;
      }>>();

      mongoSummary.pairedInstruments.forEach(instrument => {
        const year = "2025"; // Default year since MongoDB data might not have years
        if (!yearMap.has(year)) {
          yearMap.set(year, []);
        }
        yearMap.get(year)!.push(instrument);
      });

      // Convert year map to pairedCommitments format
      yearMap.forEach((instruments, year) => {
        const totalCommitment = instruments.reduce((sum, inst) => sum + inst.commitment, 0);
        const subtotal = instruments.reduce((sum, inst) => sum + inst.outstanding, 0);

        pairedCommitments.push({
          year,
          totalCommitment,
          subtotal,
          instruments
        });
      });

      return {
        pairedCommitments,
        anomalies: mongoSummary.anomalies.map(anomaly => ({
          name: anomaly.name,
          assetClass: anomaly.assetClass,
          outstanding: anomaly.amount
        })),
        totalOutstanding: mongoSummary.totalOutstanding,
      };
    } else if (pairedInstruments && unpairedInstruments) {
      // Convert external data to our internal format
      const pairedCommitments: Array<{
        year: string;
        totalCommitment: number;
        subtotal: number;
        instruments: Array<{
          name: string;
          assetClass: string;
          commitment: number;
          called: number;
          outstanding: number;
        }>;
      }> = [];

      const anomalies: Array<{
        name: string;
        assetClass: string;
        outstanding: number;
      }> = [];

      // Group paired instruments by year (we'll use a default year since external data might not have years)
      const yearMap = new Map<string, Array<{
        name: string;
        assetClass: string;
        commitment: number;
        called: number;
        outstanding: number;
      }>>();

      pairedInstruments.forEach(instrument => {
        const year = "2023"; // Default year for uploaded data
        if (!yearMap.has(year)) {
          yearMap.set(year, []);
        }
        yearMap.get(year)!.push({
          name: instrument.instrument,
          assetClass: instrument.assetClass,
          commitment: instrument.commitmentAmount,
          called: instrument.calledAmount,
          outstanding: instrument.commitmentAmount - instrument.calledAmount,
        });
      });

      // Convert year map to pairedCommitments format
      yearMap.forEach((instruments, year) => {
        const totalCommitment = instruments.reduce((sum, inst) => sum + inst.commitment, 0);
        const subtotal = instruments.reduce((sum, inst) => sum + inst.outstanding, 0);
        pairedCommitments.push({
          year,
          totalCommitment,
          subtotal,
          instruments,
        });
      });

      // Convert unpaired instruments to anomalies
      unpairedInstruments.forEach(instrument => {
        anomalies.push({
          name: instrument.instrument,
          assetClass: instrument.assetClass,
          outstanding: instrument.commitmentAmount,
        });
      });

      const totalOutstanding = pairedCommitments.reduce((sum, year) => sum + year.subtotal, 0) +
                              anomalies.reduce((sum, anomaly) => sum + anomaly.outstanding, 0);

      return {
        pairedCommitments,
        anomalies,
        totalOutstanding,
      };
    } else {
      // No data available - return empty structure
      return {
        pairedCommitments: [],
        anomalies: [],
        totalOutstanding: 0,
      };
    }
  }, [pairedInstruments, unpairedInstruments, mongoSummary]);

  // Filter data based on selected asset class
  const filteredData = useMemo(() => {
    if (assetClassFilter === "all") {
      return combinedData;
    } else if (assetClassFilter === "real-estate") {
      // Filter for Real Estate only
      const filteredPairedCommitments = combinedData.pairedCommitments.map(yearGroup => ({
        ...yearGroup,
        instruments: yearGroup.instruments.filter(inst => inst.assetClass === "Real Estate"),
        totalCommitment: yearGroup.instruments
          .filter(inst => inst.assetClass === "Real Estate")
          .reduce((sum, inst) => sum + inst.commitment, 0),
        subtotal: yearGroup.instruments
          .filter(inst => inst.assetClass === "Real Estate")
          .reduce((sum, inst) => sum + inst.outstanding, 0),
      })).filter(yearGroup => yearGroup.instruments.length > 0);

      const filteredAnomalies = combinedData.anomalies.filter(anomaly => anomaly.assetClass === "Real Estate");

      const totalOutstanding = filteredPairedCommitments.reduce((sum, year) => sum + year.subtotal, 0) +
                              filteredAnomalies.reduce((sum, anomaly) => sum + anomaly.outstanding, 0);

      return {
        pairedCommitments: filteredPairedCommitments,
        anomalies: filteredAnomalies,
        totalOutstanding,
      };
    } else {
      // Filter for non-Real Estate (Other instruments)
      const filteredPairedCommitments = combinedData.pairedCommitments.map(yearGroup => ({
        ...yearGroup,
        instruments: yearGroup.instruments.filter(inst => inst.assetClass !== "Real Estate"),
        totalCommitment: yearGroup.instruments
          .filter(inst => inst.assetClass !== "Real Estate")
          .reduce((sum, inst) => sum + inst.commitment, 0),
        subtotal: yearGroup.instruments
          .filter(inst => inst.assetClass !== "Real Estate")
          .reduce((sum, inst) => sum + inst.outstanding, 0),
      })).filter(yearGroup => yearGroup.instruments.length > 0);

      const filteredAnomalies = combinedData.anomalies.filter(anomaly => anomaly.assetClass !== "Real Estate");

      const totalOutstanding = filteredPairedCommitments.reduce((sum, year) => sum + year.subtotal, 0) +
                              filteredAnomalies.reduce((sum, anomaly) => sum + anomaly.outstanding, 0);

      return {
        pairedCommitments: filteredPairedCommitments,
        anomalies: filteredAnomalies,
        totalOutstanding,
      };
    }
  }, [assetClassFilter, combinedData]);

  // Generate chart data
  const chartData = useMemo(() => {
    const yearMap = new Map<string, number>();
    
    filteredData.pairedCommitments.forEach((yearGroup) => {
      yearMap.set(yearGroup.year, yearGroup.totalCommitment);
    });

    // Add anomalies as "Unspecified" year
    if (filteredData.anomalies.length > 0) {
      const anomaliesTotal = filteredData.anomalies.reduce((sum, anomaly) => sum + anomaly.outstanding, 0);
      yearMap.set("Unspecified", anomaliesTotal);
    }

    return Array.from(yearMap.entries()).map(([year, commitment]) => ({
      year,
      commitment,
    }));
  }, [filteredData]);

  const chartConfig = {
    commitment: {
      label: "Commitment",
      color: "var(--primary)",
    },
  } satisfies ChartConfig;

  // Calculate totals
  const totalCommitted = filteredData.pairedCommitments.reduce(
    (sum, year) => sum + year.totalCommitment, 0
  ) + filteredData.anomalies.reduce((sum, anomaly) => sum + anomaly.outstanding, 0);

  const totalCalled = filteredData.pairedCommitments.reduce(
    (sum, year) => sum + year.instruments.reduce(
      (instrumentSum, instrument) => instrumentSum + instrument.called, 0
    ), 0
  );

  const activeInstruments = filteredData.pairedCommitments.reduce(
    (sum, year) => sum + year.instruments.length, 0
  ) + filteredData.anomalies.length;

  const handlePrint = () => {
    window.print();
  };

  const getFilterTitle = () => {
    switch (assetClassFilter) {
      case "real-estate":
        return "Real Estate Analysis";
      case "other":
        return "Instruments Analysis";
      default:
        return "Combined Investment Analysis";
    }
  };

  const getFilterDescription = () => {
    switch (assetClassFilter) {
      case "real-estate":
        return "Real Estate Investments and REIT Commitments";
      case "other":
        return "Private Markets & Venture Capital Investments";
      default:
        return "All Investment Categories Combined";
    }
  };

  return (
    <div className="flex flex-1 flex-col">
      {/* Print-only header */}
      <div className="hidden print:block print:mb-6">
        <h1 className="text-2xl font-bold text-center mb-2">{getFilterTitle()}</h1>
        <p className="text-center text-muted-foreground">{getFilterDescription()}</p>
        <p className="text-center text-sm text-muted-foreground mt-2">
          Data as of {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
        </p>
      </div>

      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Filter Controls */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <IconFilter className="h-5 w-5" />
                      {getFilterTitle()}
                    </CardTitle>
                    <CardDescription>{getFilterDescription()}</CardDescription>
                  </div>
                  <Select value={assetClassFilter} onValueChange={(value: AssetClassFilter) => setAssetClassFilter(value)}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Filter by Asset Class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Asset Classes</SelectItem>
                      <SelectItem value="real-estate">Real Estate Only</SelectItem>
                      <SelectItem value="other">Other Instruments</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
            </Card>
          </div>

          {/* Summary Cards */}
          <div className="grid gap-4 px-4 lg:px-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Outstanding
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(filteredData.totalOutstanding)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {assetClassFilter === "all" ? "All Commitments" : 
                   assetClassFilter === "real-estate" ? "Real Estate Commitments" : 
                   "Non-Real Estate Commitments"}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Committed
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(totalCommitted)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all years
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Called
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(totalCalled)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Capital deployed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Instruments
                </CardTitle>
                <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeInstruments}</div>
                <p className="text-xs text-muted-foreground">
                  Including anomalies
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Chart Section */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Commitments by Year</CardTitle>
                <CardDescription>
                  Total commitment amounts {assetClassFilter === "all" ? "across all asset classes" :
                   assetClassFilter === "real-estate" ? "for Real Estate investments" :
                   "for Private Markets & Venture Capital"}
                </CardDescription>
                <CardAction>
                  <Button variant="outline" size="sm" onClick={handlePrint}>
                    <IconPrinter className="h-4 w-4" />
                    Export
                  </Button>
                </CardAction>
              </CardHeader>
              <CardContent className="px-2 sm:p-6">
                <ChartContainer
                  config={chartConfig}
                  className="aspect-auto h-[250px] w-full"
                >
                  <BarChart
                    accessibilityLayer
                    data={chartData}
                    margin={{
                      left: 12,
                      right: 12,
                    }}
                  >
                    <CartesianGrid vertical={false} />
                    <XAxis
                      dataKey="year"
                      tickLine={false}
                      axisLine={false}
                      tickMargin={8}
                      tickFormatter={(value) => value}
                    />
                    <ChartTooltip
                      cursor={false}
                      content={<ChartTooltipContent hideLabel />}
                    />
                    <Bar
                      dataKey="commitment"
                      fill="var(--color-commitment)"
                      radius={8}
                    />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Paired Commitments Table */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Paired Commitments</CardTitle>
                <CardDescription>
                  Detailed breakdown of committed, called, and outstanding capital
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Instrument</TableHead>
                      <TableHead>Asset Class</TableHead>
                      <TableHead className="text-right">Commitment</TableHead>
                      <TableHead className="text-right">Called</TableHead>
                      <TableHead className="text-right">Outstanding</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.pairedCommitments.map((yearGroup) => (
                      <React.Fragment key={yearGroup.year}>
                        {/* Year Header */}
                        <TableRow className="bg-muted/50">
                          <TableCell colSpan={3} className="font-semibold">
                            {yearGroup.year}
                          </TableCell>
                          <TableCell className="text-right font-semibold">
                            {formatCurrency(yearGroup.totalCommitment)}
                          </TableCell>
                          <TableCell className="text-right font-semibold">
                            {formatCurrency(yearGroup.subtotal)}
                          </TableCell>
                        </TableRow>

                        {/* Instruments */}
                        {yearGroup.instruments.map((instrument, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {instrument.name}
                            </TableCell>
                            <TableCell className="text-muted-foreground">
                              {instrument.assetClass}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              {formatCurrency(instrument.commitment)}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              {formatCurrency(instrument.called)}
                            </TableCell>
                            <TableCell className="text-right font-mono font-semibold">
                              {formatCurrency(instrument.outstanding)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>

          {/* Anomalies Table */}
          {filteredData.anomalies.length > 0 && (
            <div className="px-4 lg:px-6">
              <Card>
                <CardHeader>
                  <CardTitle>Anomalies: Unpaired Positions</CardTitle>
                  <CardDescription>
                    Instruments with commitment-only positions requiring attention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Instrument (Commitment Only)</TableHead>
                        <TableHead>Asset Class</TableHead>
                        <TableHead className="text-right">Outstanding Commitment</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredData.anomalies.map((anomaly, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{anomaly.name}</TableCell>
                          <TableCell className="text-muted-foreground">{anomaly.assetClass}</TableCell>
                          <TableCell className="text-right font-mono font-semibold">
                            {formatCurrency(anomaly.outstanding)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          @page {
            size: A4;
            margin: 1in;
          }

          body {
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }

          .print\\:hidden {
            display: none !important;
          }

          .print\\:block {
            display: block !important;
          }

          .print\\:mb-6 {
            margin-bottom: 1.5rem !important;
          }

          /* Hide navigation and interactive elements */
          nav, .sidebar, button, .select-trigger {
            display: none !important;
          }

          /* Ensure tables have borders */
          table, th, td {
            border: 1px solid #000 !important;
            border-collapse: collapse !important;
          }

          th {
            background-color: #f5f5f5 !important;
            font-weight: bold !important;
          }

          /* Ensure proper spacing */
          .card {
            margin-bottom: 1rem !important;
            border: 1px solid #000 !important;
          }

          .card-header {
            border-bottom: 1px solid #000 !important;
            padding: 0.5rem !important;
          }

          .card-content {
            padding: 0.5rem !important;
          }

          /* Chart containers */
          .chart-container {
            height: 300px !important;
          }
        }
      `}</style>
    </div>
  );
}
