"use client";

import React, { useState, useEffect } from "react";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { DataTable } from "@/components/data-table";
import { FileUpload } from "@/components/file-upload";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IconUpload, IconDatabase, IconRefresh, IconAlertCircle, IconCheck } from "@tabler/icons-react";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useInvestmentData, useMongoConnection } from "@/hooks/use-investment-data";

// No static data imports - using MongoDB only

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  content: any;
  uploadedAt: Date;
}

export function DashboardWithMongoDB() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [useMongoData, setUseMongoData] = useState(true);
  
  // MongoDB hooks
  const { 
    data: mongoData, 
    summary, 
    loading: dataLoading, 
    error: dataError, 
    refetch,
    uploadData 
  } = useInvestmentData({ autoFetch: true });
  
  const { 
    connected, 
    loading: connectionLoading, 
    error: connectionError, 
    info: connectionInfo,
    testConnection 
  } = useMongoConnection();

  // Determine which data to use - MongoDB only now
  const currentData = useMongoData && mongoData.length > 0 ? mongoData : [];
  const hasMongoData = mongoData.length > 0;

  const handleFileUpload = async (files: UploadedFile[]) => {
    setUploadedFiles(files);
    
    if (files.length > 0 && useMongoData) {
      // Process and upload files to MongoDB
      const allData: any[] = [];
      
      files.forEach(file => {
        if (Array.isArray(file.content)) {
          allData.push(...file.content);
        } else {
          allData.push(file.content);
        }
      });

      // Transform data to match expected format
      const transformedData = allData.map((item, index) => ({
        instrument: item.instrument || item.Instrument || `Instrument ${index + 1}`,
        assetClass: item.assetClass || item["Asset Class"] || "Unknown",
        outstanding: parseFloat(item.outstanding || item["Outstanding Commitment"] || 0),
        commitment: parseFloat(item.commitment || item["Capital Commitment"] || 0),
        called: parseFloat(item.called || item["Paid Commitment"] || 0),
        status: item.status || "Active",
        year: item.year || new Date().getFullYear(),
        ...item
      }));

      const success = await uploadData(transformedData, true);
      if (success) {
        console.log('Data uploaded to MongoDB successfully');
      }
    }
  };

  const resetToDefaultData = () => {
    setUploadedFiles([]);
    setUseMongoData(false);
  };

  const switchToMongoData = () => {
    setUseMongoData(true);
    refetch();
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          
          {/* MongoDB Connection Status */}
          <div className="px-4 lg:px-6">
            <Alert className={connected ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              <div className="flex items-center gap-2">
                {connectionLoading ? (
                  <IconRefresh className="h-4 w-4 animate-spin" />
                ) : connected ? (
                  <IconCheck className="h-4 w-4 text-green-600" />
                ) : (
                  <IconAlertCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  {connectionLoading ? (
                    "Testing MongoDB connection..."
                  ) : connected ? (
                    <div className="flex items-center gap-4">
                      <span>MongoDB connected successfully</span>
                      {connectionInfo && (
                        <div className="flex gap-2">
                          <Badge variant="outline">
                            DB: {connectionInfo.database}
                          </Badge>
                          <Badge variant="outline">
                            Collections: {connectionInfo.collections?.length || 0}
                          </Badge>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div>
                      MongoDB connection failed: {connectionError}
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="ml-2"
                        onClick={testConnection}
                      >
                        Retry
                      </Button>
                    </div>
                  )}
                </AlertDescription>
              </div>
            </Alert>
          </div>

          {/* Data Source Controls */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconDatabase className="h-5 w-5" />
                    <CardTitle>Data Source</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={useMongoData ? "default" : "secondary"}>
                      {useMongoData ? "MongoDB" : "Static Data"}
                    </Badge>
                    {hasMongoData && (
                      <Badge variant="outline">
                        {mongoData.length} records
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {connected && (
                    <Button
                      variant={useMongoData ? "default" : "outline"}
                      size="sm"
                      onClick={switchToMongoData}
                      disabled={dataLoading}
                    >
                      {dataLoading && <IconRefresh className="h-4 w-4 mr-2 animate-spin" />}
                      Use MongoDB Data
                    </Button>
                  )}
                  <Button
                    variant={!useMongoData ? "default" : "outline"}
                    size="sm"
                    onClick={resetToDefaultData}
                  >
                    Use Static Data
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetch}
                    disabled={dataLoading || !connected}
                  >
                    <IconRefresh className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
                
                {dataError && (
                  <Alert className="mt-4 border-red-200 bg-red-50">
                    <IconAlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription>
                      Error loading data: {dataError}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>

          {/* File Upload Section */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconUpload className="h-5 w-5" />
                    <CardTitle>Upload Data to MongoDB</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    {uploadedFiles.length > 0 && (
                      <Badge variant="secondary">
                        {uploadedFiles.length} file(s) uploaded
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowFileUpload(!showFileUpload)}
                      disabled={!connected}
                    >
                      <IconUpload className="h-4 w-4 mr-2" />
                      {showFileUpload ? 'Hide Upload' : 'Upload Data'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              {showFileUpload && (
                <CardContent>
                  <FileUpload
                    onFileUpload={handleFileUpload}
                    maxFiles={3}
                  />
                  <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Upload to MongoDB:</h4>
                    <p className="text-xs text-muted-foreground">
                      Upload JSON files with investment data. The data will be automatically 
                      processed and stored in your MongoDB collection, then displayed in the dashboard.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
          </div>

          {/* Summary Cards */}
          {summary && (
            <div className="px-4 lg:px-6">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Outstanding</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${summary.totalOutstanding.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Commitment</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${summary.totalCommitment.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Called</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${summary.totalCalled.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Instruments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {summary.totalInstruments}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Chart Section */}
          <div className="px-4 lg:px-6">
            <ChartAreaInteractive />
          </div>

          {/* Data Table Section */}
          <DataTable data={currentData} />
        </div>
      </div>
    </div>
  );
}
