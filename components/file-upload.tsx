"use client";

import React, { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { IconUpload, IconFile, IconX, IconCheck } from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UploadedFile, useFileManager } from "@/lib/file-manager";

interface FileUploadProps {
  onFileUpload?: (files: UploadedFile[]) => void;
  maxFiles?: number;
  acceptedFileTypes?: string[];
  className?: string;
}

export function FileUpload({
  onFileUpload,
  maxFiles = 5,
  acceptedFileTypes = ['.json'],
  className = ""
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { loadFiles, addFile, removeFile } = useFileManager();

  useEffect(() => {
    const files = loadFiles();
    setUploadedFiles(files);
    onFileUpload?.(files);
  }, []);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true);
    
    try {
      const newFiles: UploadedFile[] = [];
      
      for (const file of acceptedFiles) {
        if (file.type === 'application/json' || file.name.endsWith('.json')) {
          const content = await file.text();
          try {
            const parsedContent = JSON.parse(content);
            const uploadedFile: UploadedFile = {
              id: Math.random().toString(36).substr(2, 9),
              name: file.name,
              size: file.size,
              content: parsedContent,
              uploadedAt: new Date(),
              type: 'json',
            };
            newFiles.push(uploadedFile);
          } catch (error) {
            console.error(`Error parsing JSON file ${file.name}:`, error);
          }
        }
      }
      
      // Add files to persistent storage
      for (const file of newFiles) {
        addFile(file);
      }

      const updatedFiles = loadFiles().slice(0, maxFiles);
      setUploadedFiles(updatedFiles);
      onFileUpload?.(updatedFiles);
    } catch (error) {
      console.error('Error processing files:', error);
    } finally {
      setIsUploading(false);
    }
  }, [uploadedFiles, maxFiles, onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/json': acceptedFileTypes,
    },
    maxFiles,
    disabled: isUploading,
  });

  const handleRemoveFile = (fileId: string) => {
    const updatedFiles = removeFile(fileId);
    setUploadedFiles(updatedFiles);
    onFileUpload?.(updatedFiles);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-muted-foreground/50'
              }
              ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input {...getInputProps()} />
            <IconUpload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                {isDragActive ? 'Drop JSON files here' : 'Upload JSON files'}
              </p>
              <p className="text-sm text-muted-foreground">
                Drag and drop JSON files here, or click to select files
              </p>
              <p className="text-xs text-muted-foreground">
                Maximum {maxFiles} files • JSON format only
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {uploadedFiles.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">Uploaded Files</h3>
            <div className="space-y-2">
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <IconFile className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(file.size)} • {file.uploadedAt.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-xs">
                      <IconCheck className="h-3 w-3 mr-1" />
                      JSON
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFile(file.id)}
                      className="h-8 w-8 p-0"
                    >
                      <IconX className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
