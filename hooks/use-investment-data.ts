import { useState, useEffect, useCallback } from 'react';

interface InvestmentData {
  _id?: string;
  instrument: string;
  assetClass: string;
  outstanding?: number;
  commitment?: number;
  called?: number;
  status?: string;
  year?: number;
  [key: string]: any;
}

interface InvestmentSummary {
  totalOutstanding: number;
  totalCommitment: number;
  totalCalled: number;
  assetClasses: string[];
  yearlyBreakdown: Record<number, number>;
  statusBreakdown: Record<string, number>;
  totalInstruments: number;
}

interface UseInvestmentDataReturn {
  data: InvestmentData[];
  summary: InvestmentSummary | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  uploadData: (data: InvestmentData[], replace?: boolean) => Promise<boolean>;
}

interface UseInvestmentDataOptions {
  assetClass?: string;
  status?: string;
  year?: number;
  autoFetch?: boolean;
}

export function useInvestmentData(options: UseInvestmentDataOptions = {}): UseInvestmentDataReturn {
  const [data, setData] = useState<InvestmentData[]>([]);
  const [summary, setSummary] = useState<InvestmentSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (options.assetClass) params.append('assetClass', options.assetClass);
      if (options.status) params.append('status', options.status);
      if (options.year) params.append('year', options.year.toString());

      const response = await fetch(`/api/investments?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch data');
      }

      setData(result.data || []);
      setSummary(result.processed || null);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching investment data:', err);
    } finally {
      setLoading(false);
    }
  }, [options.assetClass, options.status, options.year]);

  const uploadData = useCallback(async (newData: InvestmentData[], replace = false): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/investments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: newData,
          replace
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to upload data');
      }

      // Refetch data after successful upload
      await fetchData();
      return true;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error uploading investment data:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [fetchData]);

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchData();
    }
  }, [fetchData, options.autoFetch]);

  return {
    data,
    summary,
    loading,
    error,
    refetch: fetchData,
    uploadData
  };
}

// Hook for testing MongoDB connection
export function useMongoConnection() {
  const [connected, setConnected] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [info, setInfo] = useState<any>(null);

  const testConnection = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/test-connection');
      const result = await response.json();
      
      setConnected(result.success);
      setInfo(result);
      
      if (!result.success) {
        setError(result.error || 'Connection failed');
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setConnected(false);
      console.error('Error testing MongoDB connection:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    testConnection();
  }, [testConnection]);

  return {
    connected,
    loading,
    error,
    info,
    testConnection
  };
}
