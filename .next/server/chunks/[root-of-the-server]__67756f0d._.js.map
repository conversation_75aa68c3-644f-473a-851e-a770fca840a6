{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nif (!process.env.MONGODB_URI) {\n  throw new Error('Invalid/Missing environment variable: \"MONGODB_URI\"');\n}\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nconsole.log('MongoDB URI loaded:', MONGODB_URI ? 'URI found' : 'URI missing');\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Cache the database connection in development to prevent multiple connections\nlet cached: MongooseCache = (global as any).mongoose;\n\nif (!cached) {\n  cached = (global as any).mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      serverSelectionTimeoutMS: 10000, // 10 seconds timeout\n      socketTimeoutMS: 45000, // 45 seconds socket timeout\n      maxPoolSize: 10, // Maintain up to 10 socket connections\n      minPoolSize: 5, // Maintain a minimum of 5 socket connections\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n\n// Connection status helper\nexport async function testConnection(): Promise<boolean> {\n  try {\n    await connectDB();\n    return mongoose.connection.readyState === 1;\n  } catch (error) {\n    console.error('MongoDB connection failed:', error);\n    return false;\n  }\n}\n\n// Get connection info\nexport async function getConnectionInfo() {\n  try {\n    await connectDB();\n    const db = mongoose.connection.db;\n    const collections = await db.listCollections().toArray();\n\n    return {\n      connected: mongoose.connection.readyState === 1,\n      database: mongoose.connection.name,\n      collections: collections.map(col => col.name),\n      host: mongoose.connection.host,\n      port: mongoose.connection.port\n    };\n  } catch (error) {\n    console.error('Error getting connection info:', error);\n    return {\n      connected: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n// MDE Schema Definition - flexible schema to match actual data\nconst mdeSchema = new mongoose.Schema({}, {\n  strict: false,  // Allow any fields\n  collection: 'MDE'\n});\n\n// Export the MDE model directly\nexport const MDE = mongoose.models.MDE || mongoose.model('MDE', mdeSchema);\n\n// Keep Investment as alias for backward compatibility, but point to MDE\nexport const Investment = MDE;\n\n// Generic CRUD operations using Mongoose\nexport class MongoDBService {\n  static async create(): Promise<MongoDBService> {\n    await connectDB();\n    return new MongoDBService();\n  }\n\n  async findAll<T>(modelName: string, filter = {}): Promise<T[]> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.find(filter).lean();\n  }\n\n  async findOne<T>(modelName: string, filter: any): Promise<T | null> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.findOne(filter).lean();\n  }\n\n  async insertOne<T>(modelName: string, document: T): Promise<any> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    const instance = new Model(document);\n    return await instance.save();\n  }\n\n  async insertMany<T>(modelName: string, documents: T[]): Promise<any> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.insertMany(documents);\n  }\n\n  async updateOne<T>(modelName: string, filter: any, update: any): Promise<any> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.updateOne(filter, { $set: { ...update, updatedAt: new Date() } });\n  }\n\n  async deleteOne<T>(modelName: string, filter: any): Promise<any> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.deleteOne(filter);\n  }\n\n  async deleteMany<T>(modelName: string, filter: any): Promise<any> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.deleteMany(filter);\n  }\n\n  async count(modelName: string, filter = {}): Promise<number> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.countDocuments(filter);\n  }\n\n  async aggregate<T>(modelName: string, pipeline: any[]): Promise<T[]> {\n    await connectDB();\n    const Model = mongoose.models[modelName];\n    if (!Model) {\n      throw new Error(`Model ${modelName} not found`);\n    }\n    return await Model.aggregate(pipeline);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,EAAE;IAC5B,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAC3C,QAAQ,GAAG,CAAC,uBAAuB,cAAc,cAAc;AAO/D,+EAA+E;AAC/E,IAAI,SAAwB,AAAC,OAAe,QAAQ;AAEpD,IAAI,CAAC,QAAQ;IACX,SAAS,AAAC,OAAe,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AAClE;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,0BAA0B;YAC1B,iBAAiB;YACjB,aAAa;YACb,aAAa;QACf;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IACjD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe;AAGR,eAAe;IACpB,IAAI;QACF,MAAM;QACN,OAAO,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,MAAM,KAAK,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE;QACjC,MAAM,cAAc,MAAM,GAAG,eAAe,GAAG,OAAO;QAEtD,OAAO;YACL,WAAW,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK;YAC9C,UAAU,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;YAClC,aAAa,YAAY,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;YAC5C,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;YAC9B,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,WAAW;YACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA,+DAA+D;AAC/D,MAAM,YAAY,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,CAAC,GAAG;IACxC,QAAQ;IACR,YAAY;AACd;AAGO,MAAM,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,OAAO;AAGzD,MAAM,aAAa;AAGnB,MAAM;IACX,aAAa,SAAkC;QAC7C,MAAM;QACN,OAAO,IAAI;IACb;IAEA,MAAM,QAAW,SAAiB,EAAE,SAAS,CAAC,CAAC,EAAgB;QAC7D,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,IAAI,CAAC,QAAQ,IAAI;IACtC;IAEA,MAAM,QAAW,SAAiB,EAAE,MAAW,EAAqB;QAClE,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI;IACzC;IAEA,MAAM,UAAa,SAAiB,EAAE,QAAW,EAAgB;QAC/D,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,MAAM,WAAW,IAAI,MAAM;QAC3B,OAAO,MAAM,SAAS,IAAI;IAC5B;IAEA,MAAM,WAAc,SAAiB,EAAE,SAAc,EAAgB;QACnE,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,UAAU,CAAC;IAChC;IAEA,MAAM,UAAa,SAAiB,EAAE,MAAW,EAAE,MAAW,EAAgB;QAC5E,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,SAAS,CAAC,QAAQ;YAAE,MAAM;gBAAE,GAAG,MAAM;gBAAE,WAAW,IAAI;YAAO;QAAE;IACpF;IAEA,MAAM,UAAa,SAAiB,EAAE,MAAW,EAAgB;QAC/D,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,SAAS,CAAC;IAC/B;IAEA,MAAM,WAAc,SAAiB,EAAE,MAAW,EAAgB;QAChE,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,UAAU,CAAC;IAChC;IAEA,MAAM,MAAM,SAAiB,EAAE,SAAS,CAAC,CAAC,EAAmB;QAC3D,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,cAAc,CAAC;IACpC;IAEA,MAAM,UAAa,SAAiB,EAAE,QAAe,EAAgB;QACnE,MAAM;QACN,MAAM,QAAQ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU;QACxC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC;QAChD;QACA,OAAO,MAAM,MAAM,SAAS,CAAC;IAC/B;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/app/api/test-connection/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { testConnection, getConnectionInfo, Investment } from '@/lib/mongodb';\nimport connectDB from '@/lib/mongodb';\n\nexport async function GET() {\n  try {\n    // Ensure database connection\n    await connectDB();\n\n    // Test basic connection\n    const isConnected = await testConnection();\n\n    if (!isConnected) {\n      return NextResponse.json({\n        success: false,\n        error: 'Failed to connect to MongoDB',\n        connected: false\n      }, { status: 500 });\n    }\n\n    // Get database info\n    const connectionInfo = await getConnectionInfo();\n\n    // Get MDE collection data using Mongoose model\n    const investmentCount = await Investment.countDocuments();\n    const sampleData = await Investment.find({}).limit(3).lean();\n\n    return NextResponse.json({\n      success: true,\n      connected: true,\n      ...connectionInfo,\n      investmentCount,\n      sampleData,\n      timestamp: new Date().toISOString()\n    });\n\n  } catch (error) {\n    console.error('Connection test error:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Connection test failed',\n      details: error instanceof Error ? error.message : 'Unknown error',\n      connected: false\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAGO,eAAe;IACpB,IAAI;QACF,6BAA6B;QAC7B,MAAM,CAAA,GAAA,gHAAA,CAAA,UAAS,AAAD;QAEd,wBAAwB;QACxB,MAAM,cAAc,MAAM,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD;QAEvC,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,WAAW;YACb,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,CAAA,GAAA,gHAAA,CAAA,oBAAiB,AAAD;QAE7C,+CAA+C;QAC/C,MAAM,kBAAkB,MAAM,gHAAA,CAAA,aAAU,CAAC,cAAc;QACvD,MAAM,aAAa,MAAM,gHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI;QAE1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,WAAW;YACX,GAAG,cAAc;YACjB;YACA;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,WAAW;QACb,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}