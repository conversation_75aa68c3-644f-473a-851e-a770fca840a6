{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/active-theme.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ActiveThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ActiveThemeProvider() from the server but ActiveThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/active-theme.tsx <module evaluation>\",\n    \"ActiveThemeProvider\",\n);\nexport const useThemeConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeConfig() from the server but useThemeConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/active-theme.tsx <module evaluation>\",\n    \"useThemeConfig\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,6DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,6DACA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/active-theme.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ActiveThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ActiveThemeProvider() from the server but ActiveThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/active-theme.tsx\",\n    \"ActiveThemeProvider\",\n);\nexport const useThemeConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeConfig() from the server but useThemeConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/active-theme.tsx\",\n    \"useThemeConfig\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,yCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yCACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/app/layout.tsx"], "sourcesContent": ["import { cookies } from \"next/headers\";\nimport type { Metada<PERSON> } from \"next\";\n\nimport \"./globals.css\";\n\nimport { cn } from \"@/lib/utils\";\n\nimport { ThemeProvider } from \"@/components/providers/theme-provider\";\nimport { ActiveThemeProvider } from \"@/components/active-theme\";\n\nexport const metadata: Metadata = {\n  title: \"YadCap Dashboard\",\n  description:\n    \"A fully responsive analytics dashboard featuring dynamic charts, interactive tables, a collapsible sidebar, and a light/dark mode theme switcher. Built with modern web technologies, it ensures seamless performance across devices, offering an intuitive user interface for data visualization and exploration.\",\n};\n\nexport default async function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  const cookieStore = await cookies();\n  const activeThemeValue = cookieStore.get(\"active_theme\")?.value;\n  const isScaled = activeThemeValue?.endsWith(\"-scaled\");\n\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body\n        className={cn(\n          \"bg-background overscroll-none font-sans antialiased\",\n          activeThemeValue ? `theme-${activeThemeValue}` : \"\",\n          isScaled ? \"theme-scaled\" : \"\"\n        )}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"light\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <ActiveThemeProvider>\n            {children}\n          </ActiveThemeProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAKA;AAEA;AACA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aACE;AACJ;AAEe,eAAe,WAAW,EACvC,QAAQ,EAGR;IACA,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,mBAAmB,YAAY,GAAG,CAAC,iBAAiB;IAC1D,MAAM,WAAW,kBAAkB,SAAS;IAE5C,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uDACA,mBAAmB,CAAC,MAAM,EAAE,kBAAkB,GAAG,IACjD,WAAW,iBAAiB;sBAG9B,cAAA,8OAAC,6IAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAEzB,cAAA,8OAAC,8HAAA,CAAA,sBAAmB;8BACjB;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}