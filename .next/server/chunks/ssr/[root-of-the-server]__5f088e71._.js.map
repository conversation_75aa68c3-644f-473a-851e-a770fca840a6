{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: React.ComponentProps<typeof NextThemesProvider>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/active-theme.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ReactNode,\n  createContext,\n  useContext,\n  useEffect,\n  useState,\n} from \"react\";\n\nconst COOKIE_NAME = \"active_theme\";\nconst DEFAULT_THEME = \"default\";\n\nfunction setThemeCookie(theme: string) {\n  if (typeof window === \"undefined\") return;\n\n  document.cookie = `${COOKIE_NAME}=${theme}; path=/; max-age=31536000; SameSite=Lax; ${\n    window.location.protocol === \"https:\" ? \"Secure;\" : \"\"\n  }`;\n}\n\ntype ThemeContextType = {\n  activeTheme: string;\n  setActiveTheme: (theme: string) => void;\n};\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ActiveThemeProvider({\n  children,\n  initialTheme,\n}: {\n  children: ReactNode;\n  initialTheme?: string;\n}) {\n  const [activeTheme, setActiveTheme] = useState<string>(\n    () => initialTheme || DEFAULT_THEME\n  );\n\n  useEffect(() => {\n    setThemeCookie(activeTheme);\n\n    Array.from(document.body.classList)\n      .filter((className) => className.startsWith(\"theme-\"))\n      .forEach((className) => {\n        document.body.classList.remove(className);\n      });\n    document.body.classList.add(`theme-${activeTheme}`);\n    if (activeTheme.endsWith(\"-scaled\")) {\n      document.body.classList.add(\"theme-scaled\");\n    }\n  }, [activeTheme]);\n\n  return (\n    <ThemeContext.Provider value={{ activeTheme, setActiveTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useThemeConfig() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error(\n      \"useThemeConfig must be used within an ActiveThemeProvider\"\n    );\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUA,MAAM,cAAc;AACpB,MAAM,gBAAgB;AAEtB,SAAS,eAAe,KAAa;IACnC,wCAAmC;;AAKrC;AAOA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,oBAAoB,EAClC,QAAQ,EACR,YAAY,EAIb;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3C,IAAM,gBAAgB;IAGxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QAEf,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAC/B,MAAM,CAAC,CAAC,YAAc,UAAU,UAAU,CAAC,WAC3C,OAAO,CAAC,CAAC;YACR,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;QACF,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa;QAClD,IAAI,YAAY,QAAQ,CAAC,YAAY;YACnC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAa;QAAe;kBACzD;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,qMAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,MAAI,WAAS,MAAI,IAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE;QAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;QAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC,IAAE,CAAA;YAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;QAAC;QAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;YAAE,EAAE,KAAK,CAAC,WAAW,GAAC;QAAC;QAAC,KAAG,QAAM;IAAG,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;QAAE,EAAE;QAAG,IAAG;YAAC,aAAa,OAAO,CAAC,GAAE;QAAE,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,OAAO,UAAU,CAAC;QAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE,IAAG,IAAI,EAAE,cAAc,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,CAAA;YAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;QAAC;QAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU,IAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,EAAE,KAAG,OAAK,IAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,aAAY;YAAE,eAAc,MAAI,WAAS,IAAE;YAAE,QAAO,IAAE;mBAAI;gBAAE;aAAS,GAAC;YAAE,aAAY,IAAE,IAAE,KAAK;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}]}