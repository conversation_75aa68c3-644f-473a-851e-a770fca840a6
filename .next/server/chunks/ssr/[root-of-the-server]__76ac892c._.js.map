{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,4HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,0HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,0HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,0HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,0HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,8HAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAO;;0BACN,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,4HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/nav-main.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { IconCirclePlusFilled, IconMail, type Icon } from \"@tabler/icons-react\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  SidebarGroup,\n  SidebarGroupContent,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n} from \"@/components/ui/sidebar\"\n\nexport function NavMain({\n  items,\n}: {\n  items: {\n    title: string\n    url: string\n    icon?: Icon\n  }[]\n}) {\n  return (\n    <SidebarGroup>\n      <SidebarGroupContent className=\"flex flex-col gap-2\">\n        <SidebarMenu>\n          <SidebarMenuItem className=\"flex items-center gap-2\">\n            <SidebarMenuButton\n              tooltip=\"Quick Create\"\n              className=\"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear\"\n            >\n              <IconCirclePlusFilled />\n              <span>Quick Create</span>\n            </SidebarMenuButton>\n            <Button\n              size=\"icon\"\n              className=\"size-8 group-data-[collapsible=icon]:opacity-0\"\n              variant=\"outline\"\n            >\n              <IconMail />\n              <span className=\"sr-only\">Inbox</span>\n            </Button>\n          </SidebarMenuItem>\n        </SidebarMenu>\n        <SidebarMenu>\n          {items.map((item) => (\n            <SidebarMenuItem key={item.title}>\n              <SidebarMenuButton tooltip={item.title} asChild>\n                <Link href={item.url}>\n                  {item.icon && <item.icon />}\n                  <span>{item.title}</span>\n                </Link>\n              </SidebarMenuButton>\n            </SidebarMenuItem>\n          ))}\n        </SidebarMenu>\n      </SidebarGroupContent>\n    </SidebarGroup>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AANA;;;;;;AAcO,SAAS,QAAQ,EACtB,KAAK,EAON;IACC,qBACE,8OAAC,4HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,4HAAA,CAAA,sBAAmB;YAAC,WAAU;;8BAC7B,8OAAC,4HAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4HAAA,CAAA,kBAAe;wBAAC,WAAU;;0CACzB,8OAAC,4HAAA,CAAA,oBAAiB;gCAChB,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,8OAAA,CAAA,uBAAoB;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAQ;;kDAER,8OAAC,sNAAA,CAAA,WAAQ;;;;;kDACT,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIhC,8OAAC,4HAAA,CAAA,cAAW;8BACT,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;gCAAC,SAAS,KAAK,KAAK;gCAAE,OAAO;0CAC7C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,GAAG;;wCACjB,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;;;;;sDACxB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;2BAJD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAa5C", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/nav-secondary.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { type Icon } from \"@tabler/icons-react\"\n\nimport {\n  SidebarGroup,\n  SidebarGroupContent,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n} from \"@/components/ui/sidebar\"\n\nexport function NavSecondary({\n  items,\n  ...props\n}: {\n  items: {\n    title: string\n    url: string\n    icon: Icon\n  }[]\n} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {\n  return (\n    <SidebarGroup {...props}>\n      <SidebarGroupContent>\n        <SidebarMenu>\n          {items.map((item) => (\n            <SidebarMenuItem key={item.title}>\n              <SidebarMenuButton asChild>\n                <a href={item.url}>\n                  <item.icon />\n                  <span>{item.title}</span>\n                </a>\n              </SidebarMenuButton>\n            </SidebarMenuItem>\n          ))}\n        </SidebarMenu>\n      </SidebarGroupContent>\n    </SidebarGroup>\n  )\n}\n"], "names": [], "mappings": ";;;;AAKA;AALA;;;AAaO,SAAS,aAAa,EAC3B,KAAK,EACL,GAAG,OAOkD;IACrD,qBACE,8OAAC,4HAAA,CAAA,eAAY;QAAE,GAAG,KAAK;kBACrB,cAAA,8OAAC,4HAAA,CAAA,sBAAmB;sBAClB,cAAA,8OAAC,4HAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;4BAAC,OAAO;sCACxB,cAAA,8OAAC;gCAAE,MAAM,KAAK,GAAG;;kDACf,8OAAC,KAAK,IAAI;;;;;kDACV,8OAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAJD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;AAa5C", "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/nav-user.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  IconCreditCard,\n  IconDotsVertical,\n  IconLogout,\n  IconNotification,\n  IconUserCircle,\n} from \"@tabler/icons-react\"\n\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from \"@/components/ui/avatar\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  useSidebar,\n} from \"@/components/ui/sidebar\"\n\nexport function NavUser({\n  user,\n}: {\n  user: {\n    name: string\n    email: string\n    avatar: string\n  }\n}) {\n  const { isMobile } = useSidebar()\n\n  return (\n    <SidebarMenu>\n      <SidebarMenuItem>\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <SidebarMenuButton\n              size=\"lg\"\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\n            >\n              <Avatar className=\"h-8 w-8 rounded-lg grayscale\">\n                <AvatarImage src={user.avatar} alt={user.name} />\n                <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\n              </Avatar>\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                <span className=\"truncate font-medium\">{user.name}</span>\n                <span className=\"text-muted-foreground truncate text-xs\">\n                  {user.email}\n                </span>\n              </div>\n              <IconDotsVertical className=\"ml-auto size-4\" />\n            </SidebarMenuButton>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\n            side={isMobile ? \"bottom\" : \"right\"}\n            align=\"end\"\n            sideOffset={4}\n          >\n            <DropdownMenuLabel className=\"p-0 font-normal\">\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\n                <Avatar className=\"h-8 w-8 rounded-lg\">\n                  <AvatarImage src={user.avatar} alt={user.name} />\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\n                </Avatar>\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                  <span className=\"truncate font-medium\">{user.name}</span>\n                  <span className=\"text-muted-foreground truncate text-xs\">\n                    {user.email}\n                  </span>\n                </div>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuGroup>\n              <DropdownMenuItem>\n                <IconUserCircle />\n                Account\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <IconCreditCard />\n                Billing\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <IconNotification />\n                Notifications\n              </DropdownMenuItem>\n            </DropdownMenuGroup>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <IconLogout />\n              Log out\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </SidebarMenuItem>\n    </SidebarMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AAKA;AASA;AAxBA;;;;;;AA+BO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,8OAAC,4HAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,4HAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,qIAAA,CAAA,eAAY;;kCACX,8OAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,2HAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,2HAAA,CAAA,iBAAc;4CAAC,WAAU;sDAAa;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAwB,KAAK,IAAI;;;;;;sDACjD,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;;;;;;;8CAGf,8OAAC,sOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGhC,8OAAC,qIAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,qIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,2HAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,2HAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKnB,8OAAC,qIAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,qIAAA,CAAA,oBAAiB;;kDAChB,8OAAC,qIAAA,CAAA,mBAAgB;;0DACf,8OAAC,kOAAA,CAAA,iBAAc;;;;;4CAAG;;;;;;;kDAGpB,8OAAC,qIAAA,CAAA,mBAAgB;;0DACf,8OAAC,kOAAA,CAAA,iBAAc;;;;;4CAAG;;;;;;;kDAGpB,8OAAC,qIAAA,CAAA,mBAAgB;;0DACf,8OAAC,sOAAA,CAAA,mBAAgB;;;;;4CAAG;;;;;;;;;;;;;0CAIxB,8OAAC,qIAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,qIAAA,CAAA,mBAAgB;;kDACf,8OAAC,0NAAA,CAAA,aAAU;;;;;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport {\n  IconCamera,\n  IconChartBar,\n  IconDashboard,\n  IconFileAi,\n  IconFileDescription,\n  IconHelp,\n  IconInnerShadowTop,\n  IconMessageCircle,\n  IconSearch,\n  IconSettings,\n} from \"@tabler/icons-react\";\n\nimport { NavMain } from \"@/components/nav-main\";\nimport { NavSecondary } from \"@/components/nav-secondary\";\nimport { NavUser } from \"@/components/nav-user\";\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarHeader,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n} from \"@/components/ui/sidebar\";\n\nconst data = {\n  user: {\n    name: \"shadcn\",\n    email: \"<EMAIL>\",\n    avatar: \"/avatars/shadcn.jpg\",\n  },\n  navMain: [\n    {\n      title: \"Dashboard\",\n      url: \"/dashboard\",\n      icon: IconDashboard,\n    },\n    {\n      title: \"Chat\",\n      url: \"/chat\",\n      icon: IconMessageCircle,\n    },\n    {\n      title: \"Investment Analysis\",\n      url: \"/analysis\",\n      icon: IconChartBar,\n    },\n  ],\n  navClouds: [\n    {\n      title: \"Capture\",\n      icon: IconCamera,\n      isActive: true,\n      url: \"#\",\n      items: [\n        {\n          title: \"Active Proposals\",\n          url: \"#\",\n        },\n        {\n          title: \"Archived\",\n          url: \"#\",\n        },\n      ],\n    },\n    {\n      title: \"Proposal\",\n      icon: IconFileDescription,\n      url: \"#\",\n      items: [\n        {\n          title: \"Active Proposals\",\n          url: \"#\",\n        },\n        {\n          title: \"Archived\",\n          url: \"#\",\n        },\n      ],\n    },\n    {\n      title: \"Prompts\",\n      icon: IconFileAi,\n      url: \"#\",\n      items: [\n        {\n          title: \"Active Proposals\",\n          url: \"#\",\n        },\n        {\n          title: \"Archived\",\n          url: \"#\",\n        },\n      ],\n    },\n  ],\n  navSecondary: [\n    {\n      title: \"Settings\",\n      url: \"#\",\n      icon: IconSettings,\n    },\n    {\n      title: \"Get Help\",\n      url: \"#\",\n      icon: IconHelp,\n    },\n    {\n      title: \"Search\",\n      url: \"#\",\n      icon: IconSearch,\n    },\n  ],\n};\n\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\n  return (\n    <Sidebar collapsible=\"icon\" {...props}>\n      <SidebarHeader>\n        <SidebarMenu>\n          <SidebarMenuItem>\n            <SidebarMenuButton\n              asChild\n              className=\"data-[slot=sidebar-menu-button]:!p-1.5\"\n            >\n              <a href=\"#\">\n                <IconInnerShadowTop className=\"!size-5\" />\n                <span className=\"text-base font-semibold\">\n                  YadCap Dashboard\n                </span>\n              </a>\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n        </SidebarMenu>\n      </SidebarHeader>\n      <SidebarContent>\n        <NavMain items={data.navMain} />\n        <NavSecondary items={data.navSecondary} className=\"mt-auto\" />\n      </SidebarContent>\n      <SidebarFooter>\n        <NavUser user={data.user} />\n      </SidebarFooter>\n    </Sidebar>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AAnBA;;;;;;;AA6BA,MAAM,OAAO;IACX,MAAM;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP;YACE,OAAO;YACP,KAAK;YACL,MAAM,gOAAA,CAAA,gBAAa;QACrB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,wOAAA,CAAA,oBAAiB;QACzB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,8NAAA,CAAA,eAAY;QACpB;KACD;IACD,WAAW;QACT;YACE,OAAO;YACP,MAAM,0NAAA,CAAA,aAAU;YAChB,UAAU;YACV,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,4OAAA,CAAA,sBAAmB;YACzB,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,0NAAA,CAAA,aAAU;YAChB,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;KACD;IACD,cAAc;QACZ;YACE,OAAO;YACP,KAAK;YACL,MAAM,8NAAA,CAAA,eAAY;QACpB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,sNAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,0NAAA,CAAA,aAAU;QAClB;KACD;AACH;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;IAC3E,qBACE,8OAAC,4HAAA,CAAA,UAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,8OAAC,4HAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,4HAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCAAE,MAAK;;kDACN,8OAAC,0OAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,8OAAC,4HAAA,CAAA,iBAAc;;kCACb,8OAAC,0HAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;kCAC5B,8OAAC,+HAAA,CAAA,eAAY;wBAAC,OAAO,KAAK,YAAY;wBAAE,WAAU;;;;;;;;;;;;0BAEpD,8OAAC,4HAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,0HAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 2130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-[data-slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nfunction ChartContainer({\n  id,\n  className,\n  children,\n  config,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  config: ChartConfig\n  children: React.ComponentProps<\n    typeof RechartsPrimitive.ResponsiveContainer\n  >[\"children\"]\n}) {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-slot=\"chart\"\n        data-chart={chartId}\n        className={cn(\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n}\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nfunction ChartTooltipContent({\n  active,\n  payload,\n  className,\n  indicator = \"dot\",\n  hideLabel = false,\n  hideIndicator = false,\n  label,\n  labelFormatter,\n  labelClassName,\n  formatter,\n  color,\n  nameKey,\n  labelKey,\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n  React.ComponentProps<\"div\"> & {\n    hideLabel?: boolean\n    hideIndicator?: boolean\n    indicator?: \"line\" | \"dot\" | \"dashed\"\n    nameKey?: string\n    labelKey?: string\n  }) {\n  const { config } = useChart()\n\n  const tooltipLabel = React.useMemo(() => {\n    if (hideLabel || !payload?.length) {\n      return null\n    }\n\n    const [item] = payload\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\n    const value =\n      !labelKey && typeof label === \"string\"\n        ? config[label as keyof typeof config]?.label || label\n        : itemConfig?.label\n\n    if (labelFormatter) {\n      return (\n        <div className={cn(\"font-medium\", labelClassName)}>\n          {labelFormatter(value, payload)}\n        </div>\n      )\n    }\n\n    if (!value) {\n      return null\n    }\n\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n  }, [\n    label,\n    labelFormatter,\n    payload,\n    hideLabel,\n    labelClassName,\n    config,\n    labelKey,\n  ])\n\n  if (!active || !payload?.length) {\n    return null\n  }\n\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n  return (\n    <div\n      className={cn(\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\n        className\n      )}\n    >\n      {!nestLabel ? tooltipLabel : null}\n      <div className=\"grid gap-1.5\">\n        {payload.map((item, index) => {\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n          const indicatorColor = color || item.payload.fill || item.color\n\n          return (\n            <div\n              key={item.dataKey}\n              className={cn(\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\n                indicator === \"dot\" && \"items-center\"\n              )}\n            >\n              {formatter && item?.value !== undefined && item.name ? (\n                formatter(item.value, item.name, item, index, item.payload)\n              ) : (\n                <>\n                  {itemConfig?.icon ? (\n                    <itemConfig.icon />\n                  ) : (\n                    !hideIndicator && (\n                      <div\n                        className={cn(\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\n                          {\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\n                            \"w-1\": indicator === \"line\",\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                              indicator === \"dashed\",\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\n                          }\n                        )}\n                        style={\n                          {\n                            \"--color-bg\": indicatorColor,\n                            \"--color-border\": indicatorColor,\n                          } as React.CSSProperties\n                        }\n                      />\n                    )\n                  )}\n                  <div\n                    className={cn(\n                      \"flex flex-1 justify-between leading-none\",\n                      nestLabel ? \"items-end\" : \"items-center\"\n                    )}\n                  >\n                    <div className=\"grid gap-1.5\">\n                      {nestLabel ? tooltipLabel : null}\n                      <span className=\"text-muted-foreground\">\n                        {itemConfig?.label || item.name}\n                      </span>\n                    </div>\n                    {item.value && (\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\n                        {item.value.toLocaleString()}\n                      </span>\n                    )}\n                  </div>\n                </>\n              )}\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nfunction ChartLegendContent({\n  className,\n  hideIcon = false,\n  payload,\n  verticalAlign = \"bottom\",\n  nameKey,\n}: React.ComponentProps<\"div\"> &\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n    hideIcon?: boolean\n    nameKey?: string\n  }) {\n  const { config } = useChart()\n\n  if (!payload?.length) {\n    return null\n  }\n\n  return (\n    <div\n      className={cn(\n        \"flex items-center justify-center gap-4\",\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n        className\n      )}\n    >\n      {payload.map((item) => {\n        const key = `${nameKey || item.dataKey || \"value\"}`\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n        return (\n          <div\n            key={item.value}\n            className={cn(\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\n            )}\n          >\n            {itemConfig?.icon && !hideIcon ? (\n              <itemConfig.icon />\n            ) : (\n              <div\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                style={{\n                  backgroundColor: item.color,\n                }}\n              />\n            )}\n            {itemConfig?.label}\n          </div>\n        )\n      })}\n    </div>\n  )\n}\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OAMJ;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,8OAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,8OAAC,mKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;AAEA,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;AAEA,MAAM,eAAe,uJAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EAQP;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;YACjC,OAAO;QACT;QAEA,MAAM,CAAC,KAAK,GAAG;QACf,MAAM,MAAM,GAAG,YAAY,MAAM,WAAW,MAAM,QAAQ,SAAS;QACnE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;QAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;QAElB,IAAI,gBAAgB;YAClB,qBACE,8OAAC;gBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAC/B,eAAe,OAAO;;;;;;QAG7B;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,qBAAO,8OAAC;YAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;sBAAkB;;;;;;IAC7D,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,8OAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,8OAAC;oCACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,8OAAC;oCACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,8OAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;AAEA,MAAM,cAAc,sJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EAKN;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,8OAAC;gBAEC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,8OAAC,WAAW,IAAI;;;;6CAEhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;AAEA,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 2518, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 2743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-9 px-2 min-w-9\",\n        sm: \"h-8 px-1.5 min-w-8\",\n        lg: \"h-10 px-2.5 min-w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Toggle({\n  className,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof TogglePrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <TogglePrimitive.Root\n      data-slot=\"toggle\"\n      className={cn(toggleVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Toggle, toggleVariants }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ijBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,GAAG,OAEgC;IACnC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2795, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/toggle-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\"\nimport { type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { toggleVariants } from \"@/components/ui/toggle\"\n\nconst ToggleGroupContext = React.createContext<\n  VariantProps<typeof toggleVariants>\n>({\n  size: \"default\",\n  variant: \"default\",\n})\n\nfunction ToggleGroup({\n  className,\n  variant,\n  size,\n  children,\n  ...props\n}: React.ComponentProps<typeof ToggleGroupPrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <ToggleGroupPrimitive.Root\n      data-slot=\"toggle-group\"\n      data-variant={variant}\n      data-size={size}\n      className={cn(\n        \"group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs\",\n        className\n      )}\n      {...props}\n    >\n      <ToggleGroupContext.Provider value={{ variant, size }}>\n        {children}\n      </ToggleGroupContext.Provider>\n    </ToggleGroupPrimitive.Root>\n  )\n}\n\nfunction ToggleGroupItem({\n  className,\n  children,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof ToggleGroupPrimitive.Item> &\n  VariantProps<typeof toggleVariants>) {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      data-slot=\"toggle-group-item\"\n      data-variant={context.variant || variant}\n      data-size={context.size || size}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        \"min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n}\n\nexport { ToggleGroup, ToggleGroupItem }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAGA;AACA;AAPA;;;;;;AASA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAE3C;IACA,MAAM;IACN,SAAS;AACX;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,GAAG,OAEgC;IACnC,qBACE,8OAAC,2KAAA,CAAA,OAAyB;QACxB,aAAU;QACV,gBAAc;QACd,aAAW;QACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0FACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,mBAAmB,QAAQ;YAAC,OAAO;gBAAE;gBAAS;YAAK;sBACjD;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,GAAG,OAEgC;IACnC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,qBACE,8OAAC,2KAAA,CAAA,OAAyB;QACxB,aAAU;QACV,gBAAc,QAAQ,OAAO,IAAI;QACjC,aAAW,QAAQ,IAAI,IAAI;QAC3B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,QAAQ,OAAO,IAAI;YAC5B,MAAM,QAAQ,IAAI,IAAI;QACxB,IACA,+LACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2863, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/chart-area-interactive.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Area, AreaChart, CartesianGrid, XAxis } from \"recharts\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport {\n  Card,\n  CardAction,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\"\nimport {\n  ChartConfig,\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n} from \"@/components/ui/chart\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport {\n  ToggleGroup,\n  ToggleGroupItem,\n} from \"@/components/ui/toggle-group\"\n\nexport const description = \"An interactive area chart\"\n\n// Combined data from Instruments Analysis and Real Estate Analysis\nconst chartData = [\n  { date: \"2005-01-01\", instruments: 1000000.00, realEstate: 0 },\n  { date: \"2019-01-01\", instruments: 0, realEstate: 2347700.08 },\n  { date: \"2020-01-01\", instruments: 1900000.00, realEstate: 0 },\n  { date: \"2021-01-01\", instruments: 4674334.69, realEstate: 1000000.0 },\n  { date: \"2022-01-01\", instruments: 500000.00, realEstate: 3848669.37 },\n  { date: \"2024-01-01\", instruments: 2587167.33, realEstate: 0 },\n]\n\nconst chartConfig = {\n  investments: {\n    label: \"Total Investments\",\n  },\n  instruments: {\n    label: \"Instruments Analysis\",\n    color: \"var(--primary)\",\n  },\n  realEstate: {\n    label: \"Real Estate Analysis\",\n    color: \"var(--primary)\",\n  },\n} satisfies ChartConfig\n\nexport function ChartAreaInteractive() {\n  const isMobile = useIsMobile()\n  const [timeRange, setTimeRange] = React.useState(\"all\")\n\n  React.useEffect(() => {\n    if (isMobile) {\n      setTimeRange(\"recent\")\n    }\n  }, [isMobile])\n\n  const filteredData = React.useMemo(() => {\n    if (timeRange === \"recent\") {\n      // Show only 2020 onwards for mobile\n      return chartData.filter((item) => {\n        const year = new Date(item.date).getFullYear()\n        return year >= 2020\n      })\n    } else if (timeRange === \"latest\") {\n      // Show only 2021 onwards\n      return chartData.filter((item) => {\n        const year = new Date(item.date).getFullYear()\n        return year >= 2021\n      })\n    }\n    // Show all data\n    return chartData\n  }, [timeRange])\n\n\n\n  return (\n    <Card className=\"@container/card\">\n      <CardHeader>\n        <CardTitle>Total Investments</CardTitle>\n        <CardDescription>\n          <span className=\"hidden @[540px]/card:block\">\n            Instruments Analysis & Real Estate Analysis commitments by year\n          </span>\n          <span className=\"@[540px]/card:hidden\">Investment commitments</span>\n        </CardDescription>\n        <CardAction>\n          <ToggleGroup\n            type=\"single\"\n            value={timeRange}\n            onValueChange={setTimeRange}\n            variant=\"outline\"\n            className=\"hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex\"\n          >\n            <ToggleGroupItem value=\"all\">All Years</ToggleGroupItem>\n            <ToggleGroupItem value=\"recent\">2020+</ToggleGroupItem>\n            <ToggleGroupItem value=\"latest\">2021+</ToggleGroupItem>\n          </ToggleGroup>\n          <Select value={timeRange} onValueChange={setTimeRange}>\n            <SelectTrigger\n              className=\"flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden\"\n              size=\"sm\"\n              aria-label=\"Select a value\"\n            >\n              <SelectValue placeholder=\"All Years\" />\n            </SelectTrigger>\n            <SelectContent className=\"rounded-xl\">\n              <SelectItem value=\"all\" className=\"rounded-lg\">\n                All Years\n              </SelectItem>\n              <SelectItem value=\"recent\" className=\"rounded-lg\">\n                2020+\n              </SelectItem>\n              <SelectItem value=\"latest\" className=\"rounded-lg\">\n                2021+\n              </SelectItem>\n            </SelectContent>\n          </Select>\n        </CardAction>\n      </CardHeader>\n      <CardContent className=\"px-2 pt-4 sm:px-6 sm:pt-6\">\n        <ChartContainer\n          config={chartConfig}\n          className=\"aspect-auto h-[250px] w-full\"\n        >\n          <AreaChart data={filteredData}>\n            <defs>\n              <linearGradient id=\"fillInstruments\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                <stop\n                  offset=\"5%\"\n                  stopColor=\"var(--color-instruments)\"\n                  stopOpacity={1.0}\n                />\n                <stop\n                  offset=\"95%\"\n                  stopColor=\"var(--color-instruments)\"\n                  stopOpacity={0.1}\n                />\n              </linearGradient>\n              <linearGradient id=\"fillRealEstate\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                <stop\n                  offset=\"5%\"\n                  stopColor=\"var(--color-realEstate)\"\n                  stopOpacity={0.8}\n                />\n                <stop\n                  offset=\"95%\"\n                  stopColor=\"var(--color-realEstate)\"\n                  stopOpacity={0.1}\n                />\n              </linearGradient>\n            </defs>\n            <CartesianGrid vertical={false} />\n            <XAxis\n              dataKey=\"date\"\n              tickLine={false}\n              axisLine={false}\n              tickMargin={8}\n              minTickGap={32}\n              tickFormatter={(value) => {\n                const date = new Date(value)\n                return date.getFullYear().toString()\n              }}\n            />\n            <ChartTooltip\n              cursor={false}\n              defaultIndex={isMobile ? -1 : 2}\n              content={\n                <ChartTooltipContent\n                  labelFormatter={(value) => {\n                    return `Year ${new Date(value).getFullYear()}`\n                  }}\n                  formatter={(value, name) => [\n                    new Intl.NumberFormat(\"en-US\", {\n                      style: \"currency\",\n                      currency: \"USD\",\n                    }).format(Number(value)),\n                    name === \"instruments\" ? \" Instruments Analysis \" : \" Real Estate Analysis \"\n                  ]}\n                  indicator=\"dot\"\n                />\n              }\n            />\n            <Area\n              dataKey=\"realEstate\"\n              type=\"natural\"\n              fill=\"url(#fillRealEstate)\"\n              stroke=\"var(--color-realEstate)\"\n              stackId=\"a\"\n            />\n            <Area\n              dataKey=\"instruments\"\n              type=\"natural\"\n              fill=\"url(#fillInstruments)\"\n              stroke=\"var(--color-instruments)\"\n              stackId=\"a\"\n            />\n          </AreaChart>\n        </ChartContainer>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAQA;AAMA;AAOA;AA3BA;;;;;;;;;AAgCO,MAAM,cAAc;AAE3B,mEAAmE;AACnE,MAAM,YAAY;IAChB;QAAE,MAAM;QAAc,aAAa;QAAY,YAAY;IAAE;IAC7D;QAAE,MAAM;QAAc,aAAa;QAAG,YAAY;IAAW;IAC7D;QAAE,MAAM;QAAc,aAAa;QAAY,YAAY;IAAE;IAC7D;QAAE,MAAM;QAAc,aAAa;QAAY,YAAY;IAAU;IACrE;QAAE,MAAM;QAAc,aAAa;QAAW,YAAY;IAAW;IACrE;QAAE,MAAM;QAAc,aAAa;QAAY,YAAY;IAAE;CAC9D;AAED,MAAM,cAAc;IAClB,aAAa;QACX,OAAO;IACT;IACA,aAAa;QACX,OAAO;QACP,OAAO;IACT;IACA,YAAY;QACV,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,UAAU;YACZ,aAAa;QACf;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACjC,IAAI,cAAc,UAAU;YAC1B,oCAAoC;YACpC,OAAO,UAAU,MAAM,CAAC,CAAC;gBACvB,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE,WAAW;gBAC5C,OAAO,QAAQ;YACjB;QACF,OAAO,IAAI,cAAc,UAAU;YACjC,yBAAyB;YACzB,OAAO,UAAU,MAAM,CAAC,CAAC;gBACvB,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE,WAAW;gBAC5C,OAAO,QAAQ;YACjB;QACF;QACA,gBAAgB;QAChB,OAAO;IACT,GAAG;QAAC;KAAU;IAId,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;;0CACd,8OAAC;gCAAK,WAAU;0CAA6B;;;;;;0CAG7C,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAEzC,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,oIAAA,CAAA,cAAW;gCACV,MAAK;gCACL,OAAO;gCACP,eAAe;gCACf,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,oIAAA,CAAA,kBAAe;wCAAC,OAAM;kDAAM;;;;;;kDAC7B,8OAAC,oIAAA,CAAA,kBAAe;wCAAC,OAAM;kDAAS;;;;;;kDAChC,8OAAC,oIAAA,CAAA,kBAAe;wCAAC,OAAM;kDAAS;;;;;;;;;;;;0CAElC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAW,eAAe;;kDACvC,8OAAC,2HAAA,CAAA,gBAAa;wCACZ,WAAU;wCACV,MAAK;wCACL,cAAW;kDAEX,cAAA,8OAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;gDAAM,WAAU;0DAAa;;;;;;0DAG/C,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;gDAAS,WAAU;0DAAa;;;;;;0DAGlD,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;gDAAS,WAAU;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC,0HAAA,CAAA,iBAAc;oBACb,QAAQ;oBACR,WAAU;8BAEV,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC;;kDACC,8OAAC;wCAAe,IAAG;wCAAkB,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAI,IAAG;;0DAC3D,8OAAC;gDACC,QAAO;gDACP,WAAU;gDACV,aAAa;;;;;;0DAEf,8OAAC;gDACC,QAAO;gDACP,WAAU;gDACV,aAAa;;;;;;;;;;;;kDAGjB,8OAAC;wCAAe,IAAG;wCAAiB,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAI,IAAG;;0DAC1D,8OAAC;gDACC,QAAO;gDACP,WAAU;gDACV,aAAa;;;;;;0DAEf,8OAAC;gDACC,QAAO;gDACP,WAAU;gDACV,aAAa;;;;;;;;;;;;;;;;;;0CAInB,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,UAAU;;;;;;0CACzB,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,YAAY;gCACZ,eAAe,CAAC;oCACd,MAAM,OAAO,IAAI,KAAK;oCACtB,OAAO,KAAK,WAAW,GAAG,QAAQ;gCACpC;;;;;;0CAEF,8OAAC,0HAAA,CAAA,eAAY;gCACX,QAAQ;gCACR,cAAc,WAAW,CAAC,IAAI;gCAC9B,uBACE,8OAAC,0HAAA,CAAA,sBAAmB;oCAClB,gBAAgB,CAAC;wCACf,OAAO,CAAC,KAAK,EAAE,IAAI,KAAK,OAAO,WAAW,IAAI;oCAChD;oCACA,WAAW,CAAC,OAAO,OAAS;4CAC1B,IAAI,KAAK,YAAY,CAAC,SAAS;gDAC7B,OAAO;gDACP,UAAU;4CACZ,GAAG,MAAM,CAAC,OAAO;4CACjB,SAAS,gBAAgB,2BAA2B;yCACrD;oCACD,WAAU;;;;;;;;;;;0CAIhB,8OAAC,oJAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,SAAQ;;;;;;0CAEV,8OAAC,oJAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}, {"offset": {"line": 3288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3334, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 3379, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/drawer.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Drawer as DrawerPrimitive } from \"vaul\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Drawer({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) {\n  return <DrawerPrimitive.Root data-slot=\"drawer\" {...props} />\n}\n\nfunction DrawerTrigger({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Trigger>) {\n  return <DrawerPrimitive.Trigger data-slot=\"drawer-trigger\" {...props} />\n}\n\nfunction DrawerPortal({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Portal>) {\n  return <DrawerPrimitive.Portal data-slot=\"drawer-portal\" {...props} />\n}\n\nfunction DrawerClose({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Close>) {\n  return <DrawerPrimitive.Close data-slot=\"drawer-close\" {...props} />\n}\n\nfunction DrawerOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Overlay>) {\n  return (\n    <DrawerPrimitive.Overlay\n      data-slot=\"drawer-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DrawerContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Content>) {\n  return (\n    <DrawerPortal data-slot=\"drawer-portal\">\n      <DrawerOverlay />\n      <DrawerPrimitive.Content\n        data-slot=\"drawer-content\"\n        className={cn(\n          \"group/drawer-content bg-background fixed z-50 flex h-auto flex-col\",\n          \"data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-lg data-[vaul-drawer-direction=top]:border-b\",\n          \"data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-lg data-[vaul-drawer-direction=bottom]:border-t\",\n          \"data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm\",\n          \"data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm\",\n          className\n        )}\n        {...props}\n      >\n        <div className=\"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block\" />\n        {children}\n      </DrawerPrimitive.Content>\n    </DrawerPortal>\n  )\n}\n\nfunction DrawerHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"drawer-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DrawerFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"drawer-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DrawerTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Title>) {\n  return (\n    <DrawerPrimitive.Title\n      data-slot=\"drawer-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DrawerDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Description>) {\n  return (\n    <DrawerPrimitive.Description\n      data-slot=\"drawer-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,IAAI;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,MAAM;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,KAAK;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sEACA,kQACA,uRACA,+NACA,yNACA;gBAED,GAAG,KAAK;;kCAET,8OAAC;wBAAI,WAAU;;;;;;oBACd;;;;;;;;;;;;;AAIT;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,SAAe,CAAC,KAAK;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,sIAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3681, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/data-table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  DndContext,\n  KeyboardSensor,\n  MouseSensor,\n  TouchSensor,\n  closestCenter,\n  useSensor,\n  useSensors,\n  type DragEndEvent,\n  type UniqueIdentifier,\n} from \"@dnd-kit/core\"\nimport { restrictToVerticalAxis } from \"@dnd-kit/modifiers\"\nimport {\n  SortableContext,\n  arrayMove,\n  useSortable,\n  verticalListSortingStrategy,\n} from \"@dnd-kit/sortable\"\nimport { CSS } from \"@dnd-kit/utilities\"\nimport {\n  IconChevronDown,\n  IconChevronLeft,\n  IconChevronRight,\n  IconChevronsLeft,\n  IconChevronsRight,\n  IconCircleCheckFilled,\n  IconDotsVertical,\n  IconGripVertical,\n  IconLayoutColumns,\n  IconLoader,\n  IconPlus,\n  IconTrendingUp,\n} from \"@tabler/icons-react\"\nimport {\n  ColumnDef,\n  ColumnFiltersState,\n  Row,\n  SortingState,\n  VisibilityState,\n  flexRender,\n  getCoreRowModel,\n  getFacetedRowModel,\n  getFacetedUniqueValues,\n  getFilteredRowModel,\n  getPaginationRowModel,\n  getSortedRowModel,\n  useReactTable,\n} from \"@tanstack/react-table\"\nimport { Area, AreaChart, CartesianGrid, XAxis } from \"recharts\"\nimport { toast } from \"sonner\"\nimport { z } from \"zod\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  ChartConfig,\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n} from \"@/components/ui/chart\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport {\n  Drawer,\n  DrawerClose,\n  DrawerContent,\n  DrawerDescription,\n  DrawerFooter,\n  DrawerHeader,\n  DrawerTitle,\n  DrawerTrigger,\n} from \"@/components/ui/drawer\"\nimport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\"\nimport {\n  Tabs,\n  TabsContent,\n  TabsList,\n  TabsTrigger,\n} from \"@/components/ui/tabs\"\n\nexport const schema = z.object({\n  id: z.number(),\n  instrument: z.string(),\n  assetClass: z.string(),\n  status: z.string(),\n  outstanding: z.number(),\n})\n\n// Create a separate component for the drag handle\nfunction DragHandle({ id }: { id: number }) {\n  const { attributes, listeners } = useSortable({\n    id,\n  })\n\n  return (\n    <Button\n      {...attributes}\n      {...listeners}\n      variant=\"ghost\"\n      size=\"icon\"\n      className=\"text-muted-foreground size-7 hover:bg-transparent\"\n    >\n      <IconGripVertical className=\"text-muted-foreground size-3\" />\n      <span className=\"sr-only\">Drag to reorder</span>\n    </Button>\n  )\n}\n\nconst columns: ColumnDef<z.infer<typeof schema>>[] = [\n  {\n    id: \"drag\",\n    header: () => null,\n    cell: ({ row }) => <DragHandle id={row.original.id} />,\n  },\n  {\n    id: \"select\",\n    header: ({ table }) => (\n      <div className=\"flex items-center justify-center\">\n        <Checkbox\n          checked={\n            table.getIsAllPageRowsSelected() ||\n            (table.getIsSomePageRowsSelected() && \"indeterminate\")\n          }\n          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\n          aria-label=\"Select all\"\n        />\n      </div>\n    ),\n    cell: ({ row }) => (\n      <div className=\"flex items-center justify-center\">\n        <Checkbox\n          checked={row.getIsSelected()}\n          onCheckedChange={(value) => row.toggleSelected(!!value)}\n          aria-label=\"Select row\"\n        />\n      </div>\n    ),\n    enableSorting: false,\n    enableHiding: false,\n  },\n  {\n    accessorKey: \"instrument\",\n    header: \"Instrument\",\n    cell: ({ row }) => {\n      return <TableCellViewer item={row.original} />\n    },\n    enableHiding: false,\n  },\n  {\n    accessorKey: \"assetClass\",\n    header: \"Asset Class\",\n    cell: ({ row }) => (\n      <div className=\"w-32\">\n        <Badge variant=\"outline\" className=\"text-muted-foreground px-1.5\">\n          {row.original.assetClass}\n        </Badge>\n      </div>\n    ),\n  },\n  {\n    accessorKey: \"status\",\n    header: \"Status\",\n    cell: ({ row }) => (\n      <Badge variant=\"outline\" className=\"text-muted-foreground px-1.5\">\n        {row.original.status === \"Commitment\" ? (\n          <IconCircleCheckFilled className=\"fill-green-500 dark:fill-green-400\" />\n        ) : (\n          <IconLoader />\n        )}\n        {row.original.status}\n      </Badge>\n    ),\n  },\n  {\n    accessorKey: \"outstanding\",\n    header: () => <div className=\"w-full text-right\">Outstanding</div>,\n    cell: ({ row }) => (\n      <div className=\"text-right font-medium\">\n        {new Intl.NumberFormat(\"en-US\", {\n          style: \"currency\",\n          currency: \"USD\",\n        }).format(row.original.outstanding)}\n      </div>\n    ),\n  },\n  {\n    id: \"actions\",\n    cell: () => (\n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            className=\"data-[state=open]:bg-muted text-muted-foreground flex size-8\"\n            size=\"icon\"\n          >\n            <IconDotsVertical />\n            <span className=\"sr-only\">Open menu</span>\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\" className=\"w-32\">\n          <DropdownMenuItem>Edit</DropdownMenuItem>\n          <DropdownMenuItem>Favorite</DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem variant=\"destructive\">Delete</DropdownMenuItem>\n        </DropdownMenuContent>\n      </DropdownMenu>\n    ),\n  },\n]\n\nfunction DraggableRow({ row }: { row: Row<z.infer<typeof schema>> }) {\n  const { transform, transition, setNodeRef, isDragging } = useSortable({\n    id: row.original.id,\n  })\n\n  return (\n    <TableRow\n      data-state={row.getIsSelected() && \"selected\"}\n      data-dragging={isDragging}\n      ref={setNodeRef}\n      className=\"relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80\"\n      style={{\n        transform: CSS.Transform.toString(transform),\n        transition: transition,\n      }}\n    >\n      {row.getVisibleCells().map((cell) => (\n        <TableCell key={cell.id}>\n          {flexRender(cell.column.columnDef.cell, cell.getContext())}\n        </TableCell>\n      ))}\n    </TableRow>\n  )\n}\n\nexport function DataTable({\n  data: initialData,\n}: {\n  data: z.infer<typeof schema>[]\n}) {\n  const [data, setData] = React.useState(() => initialData)\n  const [rowSelection, setRowSelection] = React.useState({})\n  const [columnVisibility, setColumnVisibility] =\n    React.useState<VisibilityState>({})\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\n    []\n  )\n  const [sorting, setSorting] = React.useState<SortingState>([])\n  const [pagination, setPagination] = React.useState({\n    pageIndex: 0,\n    pageSize: 10,\n  })\n  const sortableId = React.useId()\n  const sensors = useSensors(\n    useSensor(MouseSensor, {}),\n    useSensor(TouchSensor, {}),\n    useSensor(KeyboardSensor, {})\n  )\n\n  const dataIds = React.useMemo<UniqueIdentifier[]>(\n    () => data?.map(({ id }) => id) || [],\n    [data]\n  )\n\n  const table = useReactTable({\n    data,\n    columns,\n    state: {\n      sorting,\n      columnVisibility,\n      rowSelection,\n      columnFilters,\n      pagination,\n    },\n    getRowId: (row) => row.id.toString(),\n    enableRowSelection: true,\n    onRowSelectionChange: setRowSelection,\n    onSortingChange: setSorting,\n    onColumnFiltersChange: setColumnFilters,\n    onColumnVisibilityChange: setColumnVisibility,\n    onPaginationChange: setPagination,\n    getCoreRowModel: getCoreRowModel(),\n    getFilteredRowModel: getFilteredRowModel(),\n    getPaginationRowModel: getPaginationRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n    getFacetedRowModel: getFacetedRowModel(),\n    getFacetedUniqueValues: getFacetedUniqueValues(),\n  })\n\n  function handleDragEnd(event: DragEndEvent) {\n    const { active, over } = event\n    if (active && over && active.id !== over.id) {\n      setData((data) => {\n        const oldIndex = dataIds.indexOf(active.id)\n        const newIndex = dataIds.indexOf(over.id)\n        return arrayMove(data, oldIndex, newIndex)\n      })\n    }\n  }\n\n  return (\n    <Tabs\n      defaultValue=\"outline\"\n      className=\"w-full flex-col justify-start gap-6\"\n    >\n      <div className=\"flex items-center justify-between px-4 lg:px-6\">\n        <Label htmlFor=\"view-selector\" className=\"sr-only\">\n          View\n        </Label>\n        <Select defaultValue=\"outline\">\n          <SelectTrigger\n            className=\"flex w-fit @4xl/main:hidden\"\n            size=\"sm\"\n            id=\"view-selector\"\n          >\n            <SelectValue placeholder=\"Select a view\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"outline\">Outline</SelectItem>\n            <SelectItem value=\"past-performance\">Past Performance</SelectItem>\n            <SelectItem value=\"key-personnel\">Key Personnel</SelectItem>\n            <SelectItem value=\"focus-documents\">Focus Documents</SelectItem>\n          </SelectContent>\n        </Select>\n        <TabsList className=\"**:data-[slot=badge]:bg-muted-foreground/30 hidden **:data-[slot=badge]:size-5 **:data-[slot=badge]:rounded-full **:data-[slot=badge]:px-1 @4xl/main:flex\">\n          <TabsTrigger value=\"outline\">Outline</TabsTrigger>\n          <TabsTrigger value=\"past-performance\">\n            Past Performance <Badge variant=\"secondary\">3</Badge>\n          </TabsTrigger>\n          <TabsTrigger value=\"key-personnel\">\n            Key Personnel <Badge variant=\"secondary\">2</Badge>\n          </TabsTrigger>\n          <TabsTrigger value=\"focus-documents\">Focus Documents</TabsTrigger>\n        </TabsList>\n        <div className=\"flex items-center gap-2\">\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"outline\" size=\"sm\">\n                <IconLayoutColumns />\n                <span className=\"hidden lg:inline\">Customize Columns</span>\n                <span className=\"lg:hidden\">Columns</span>\n                <IconChevronDown />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"w-56\">\n              {table\n                .getAllColumns()\n                .filter(\n                  (column) =>\n                    typeof column.accessorFn !== \"undefined\" &&\n                    column.getCanHide()\n                )\n                .map((column) => {\n                  return (\n                    <DropdownMenuCheckboxItem\n                      key={column.id}\n                      className=\"capitalize\"\n                      checked={column.getIsVisible()}\n                      onCheckedChange={(value) =>\n                        column.toggleVisibility(!!value)\n                      }\n                    >\n                      {column.id}\n                    </DropdownMenuCheckboxItem>\n                  )\n                })}\n            </DropdownMenuContent>\n          </DropdownMenu>\n          <Button variant=\"outline\" size=\"sm\">\n            <IconPlus />\n            <span className=\"hidden lg:inline\">Add Section</span>\n          </Button>\n        </div>\n      </div>\n      <TabsContent\n        value=\"outline\"\n        className=\"relative flex flex-col gap-4 overflow-auto px-4 lg:px-6\"\n      >\n        <div className=\"overflow-hidden rounded-lg border\">\n          <DndContext\n            collisionDetection={closestCenter}\n            modifiers={[restrictToVerticalAxis]}\n            onDragEnd={handleDragEnd}\n            sensors={sensors}\n            id={sortableId}\n          >\n            <Table>\n              <TableHeader className=\"bg-muted sticky top-0 z-10\">\n                {table.getHeaderGroups().map((headerGroup) => (\n                  <TableRow key={headerGroup.id}>\n                    {headerGroup.headers.map((header) => {\n                      return (\n                        <TableHead key={header.id} colSpan={header.colSpan}>\n                          {header.isPlaceholder\n                            ? null\n                            : flexRender(\n                                header.column.columnDef.header,\n                                header.getContext()\n                              )}\n                        </TableHead>\n                      )\n                    })}\n                  </TableRow>\n                ))}\n              </TableHeader>\n              <TableBody className=\"**:data-[slot=table-cell]:first:w-8\">\n                {table.getRowModel().rows?.length ? (\n                  <SortableContext\n                    items={dataIds}\n                    strategy={verticalListSortingStrategy}\n                  >\n                    {table.getRowModel().rows.map((row) => (\n                      <DraggableRow key={row.id} row={row} />\n                    ))}\n                  </SortableContext>\n                ) : (\n                  <TableRow>\n                    <TableCell\n                      colSpan={columns.length}\n                      className=\"h-24 text-center\"\n                    >\n                      No results.\n                    </TableCell>\n                  </TableRow>\n                )}\n              </TableBody>\n            </Table>\n          </DndContext>\n        </div>\n        <div className=\"flex items-center justify-between px-4\">\n          <div className=\"text-muted-foreground hidden flex-1 text-sm lg:flex\">\n            {table.getFilteredSelectedRowModel().rows.length} of{\" \"}\n            {table.getFilteredRowModel().rows.length} row(s) selected.\n          </div>\n          <div className=\"flex w-full items-center gap-8 lg:w-fit\">\n            <div className=\"hidden items-center gap-2 lg:flex\">\n              <Label htmlFor=\"rows-per-page\" className=\"text-sm font-medium\">\n                Rows per page\n              </Label>\n              <Select\n                value={`${table.getState().pagination.pageSize}`}\n                onValueChange={(value) => {\n                  table.setPageSize(Number(value))\n                }}\n              >\n                <SelectTrigger size=\"sm\" className=\"w-20\" id=\"rows-per-page\">\n                  <SelectValue\n                    placeholder={table.getState().pagination.pageSize}\n                  />\n                </SelectTrigger>\n                <SelectContent side=\"top\">\n                  {[10, 20, 30, 40, 50].map((pageSize) => (\n                    <SelectItem key={pageSize} value={`${pageSize}`}>\n                      {pageSize}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"flex w-fit items-center justify-center text-sm font-medium\">\n              Page {table.getState().pagination.pageIndex + 1} of{\" \"}\n              {table.getPageCount()}\n            </div>\n            <div className=\"ml-auto flex items-center gap-2 lg:ml-0\">\n              <Button\n                variant=\"outline\"\n                className=\"hidden h-8 w-8 p-0 lg:flex\"\n                onClick={() => table.setPageIndex(0)}\n                disabled={!table.getCanPreviousPage()}\n              >\n                <span className=\"sr-only\">Go to first page</span>\n                <IconChevronsLeft />\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"size-8\"\n                size=\"icon\"\n                onClick={() => table.previousPage()}\n                disabled={!table.getCanPreviousPage()}\n              >\n                <span className=\"sr-only\">Go to previous page</span>\n                <IconChevronLeft />\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"size-8\"\n                size=\"icon\"\n                onClick={() => table.nextPage()}\n                disabled={!table.getCanNextPage()}\n              >\n                <span className=\"sr-only\">Go to next page</span>\n                <IconChevronRight />\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"hidden size-8 lg:flex\"\n                size=\"icon\"\n                onClick={() => table.setPageIndex(table.getPageCount() - 1)}\n                disabled={!table.getCanNextPage()}\n              >\n                <span className=\"sr-only\">Go to last page</span>\n                <IconChevronsRight />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </TabsContent>\n      <TabsContent\n        value=\"past-performance\"\n        className=\"flex flex-col px-4 lg:px-6\"\n      >\n        <div className=\"aspect-video w-full flex-1 rounded-lg border border-dashed\"></div>\n      </TabsContent>\n      <TabsContent value=\"key-personnel\" className=\"flex flex-col px-4 lg:px-6\">\n        <div className=\"aspect-video w-full flex-1 rounded-lg border border-dashed\"></div>\n      </TabsContent>\n      <TabsContent\n        value=\"focus-documents\"\n        className=\"flex flex-col px-4 lg:px-6\"\n      >\n        <div className=\"aspect-video w-full flex-1 rounded-lg border border-dashed\"></div>\n      </TabsContent>\n    </Tabs>\n  )\n}\n\nconst chartData = [\n  { month: \"January\", desktop: 186, mobile: 80 },\n  { month: \"February\", desktop: 305, mobile: 200 },\n  { month: \"March\", desktop: 237, mobile: 120 },\n  { month: \"April\", desktop: 73, mobile: 190 },\n  { month: \"May\", desktop: 209, mobile: 130 },\n  { month: \"June\", desktop: 214, mobile: 140 },\n]\n\nconst chartConfig = {\n  desktop: {\n    label: \"Desktop\",\n    color: \"var(--primary)\",\n  },\n  mobile: {\n    label: \"Mobile\",\n    color: \"var(--primary)\",\n  },\n} satisfies ChartConfig\n\nfunction TableCellViewer({ item }: { item: z.infer<typeof schema> }) {\n  const isMobile = useIsMobile()\n\n  return (\n    <Drawer direction={isMobile ? \"bottom\" : \"right\"}>\n      <DrawerTrigger asChild>\n        <Button variant=\"link\" className=\"text-foreground w-fit px-0 text-left\">\n          {item.instrument}\n        </Button>\n      </DrawerTrigger>\n      <DrawerContent>\n        <DrawerHeader className=\"gap-1\">\n          <DrawerTitle>{item.instrument}</DrawerTitle>\n          <DrawerDescription>\n            Investment details and performance overview\n          </DrawerDescription>\n        </DrawerHeader>\n        <div className=\"flex flex-col gap-4 overflow-y-auto px-4 text-sm\">\n          {!isMobile && (\n            <>\n              <ChartContainer config={chartConfig}>\n                <AreaChart\n                  accessibilityLayer\n                  data={chartData}\n                  margin={{\n                    left: 0,\n                    right: 10,\n                  }}\n                >\n                  <CartesianGrid vertical={false} />\n                  <XAxis\n                    dataKey=\"month\"\n                    tickLine={false}\n                    axisLine={false}\n                    tickMargin={8}\n                    tickFormatter={(value) => value.slice(0, 3)}\n                    hide\n                  />\n                  <ChartTooltip\n                    cursor={false}\n                    content={<ChartTooltipContent indicator=\"dot\" />}\n                  />\n                  <Area\n                    dataKey=\"mobile\"\n                    type=\"natural\"\n                    fill=\"var(--color-mobile)\"\n                    fillOpacity={0.6}\n                    stroke=\"var(--color-mobile)\"\n                    stackId=\"a\"\n                  />\n                  <Area\n                    dataKey=\"desktop\"\n                    type=\"natural\"\n                    fill=\"var(--color-desktop)\"\n                    fillOpacity={0.4}\n                    stroke=\"var(--color-desktop)\"\n                    stackId=\"a\"\n                  />\n                </AreaChart>\n              </ChartContainer>\n              <Separator />\n              <div className=\"grid gap-2\">\n                <div className=\"flex gap-2 leading-none font-medium\">\n                  Trending up by 5.2% this month{\" \"}\n                  <IconTrendingUp className=\"size-4\" />\n                </div>\n                <div className=\"text-muted-foreground\">\n                  Showing total visitors for the last 6 months. This is just\n                  some random text to test the layout. It spans multiple lines\n                  and should wrap around.\n                </div>\n              </div>\n              <Separator />\n            </>\n          )}\n          <form className=\"flex flex-col gap-4\">\n            <div className=\"flex flex-col gap-3\">\n              <Label htmlFor=\"instrument\">Instrument</Label>\n              <Input id=\"instrument\" defaultValue={item.instrument} />\n            </div>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"flex flex-col gap-3\">\n                <Label htmlFor=\"assetClass\">Asset Class</Label>\n                <Select defaultValue={item.assetClass}>\n                  <SelectTrigger id=\"assetClass\" className=\"w-full\">\n                    <SelectValue placeholder=\"Select an asset class\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Private Markets\">\n                      Private Markets\n                    </SelectItem>\n                    <SelectItem value=\"Venture Capital\">\n                      Venture Capital\n                    </SelectItem>\n                    <SelectItem value=\"Real Estate\">\n                      Real Estate\n                    </SelectItem>\n                    <SelectItem value=\"Infrastructure\">\n                      Infrastructure\n                    </SelectItem>\n                    <SelectItem value=\"Credit\">Credit</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n              <div className=\"flex flex-col gap-3\">\n                <Label htmlFor=\"status\">Status</Label>\n                <Select defaultValue={item.status}>\n                  <SelectTrigger id=\"status\" className=\"w-full\">\n                    <SelectValue placeholder=\"Select a status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Commitment\">Commitment</SelectItem>\n                    <SelectItem value=\"Called\">Called</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n            <div className=\"flex flex-col gap-3\">\n              <Label htmlFor=\"outstanding\">Outstanding Amount</Label>\n              <Input\n                id=\"outstanding\"\n                type=\"number\"\n                defaultValue={item.outstanding}\n                placeholder=\"0.00\"\n              />\n            </div>\n          </form>\n        </div>\n        <DrawerFooter>\n          <Button>Submit</Button>\n          <DrawerClose asChild>\n            <Button variant=\"outline\">Done</Button>\n          </DrawerClose>\n        </DrawerFooter>\n      </DrawerContent>\n    </Drawer>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAWA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;AAeA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AAMA;AACA;AAUA;AAQA;AACA;AACA;AAOA;AACA;AAQA;AArGA;;;;;;;;;;;;;;;;;;;;;;;;AA4GO,MAAM,SAAS,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,IAAI,+JAAA,CAAA,IAAC,CAAC,MAAM;IACZ,YAAY,+JAAA,CAAA,IAAC,CAAC,MAAM;IACpB,YAAY,+JAAA,CAAA,IAAC,CAAC,MAAM;IACpB,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM;IAChB,aAAa,+JAAA,CAAA,IAAC,CAAC,MAAM;AACvB;AAEA,kDAAkD;AAClD,SAAS,WAAW,EAAE,EAAE,EAAkB;IACxC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QAC5C;IACF;IAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACJ,GAAG,UAAU;QACb,GAAG,SAAS;QACb,SAAQ;QACR,MAAK;QACL,WAAU;;0BAEV,8OAAC,sOAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;0BAC5B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,MAAM,UAA+C;IACnD;QACE,IAAI;QACJ,QAAQ,IAAM;QACd,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,8OAAC;gBAAW,IAAI,IAAI,QAAQ,CAAC,EAAE;;;;;;IACpD;IACA;QACE,IAAI;QACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oBACP,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;oBAExC,iBAAiB,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;oBAC9D,cAAW;;;;;;;;;;;QAIjB,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oBACP,SAAS,IAAI,aAAa;oBAC1B,iBAAiB,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;oBACjD,cAAW;;;;;;;;;;;QAIjB,eAAe;QACf,cAAc;IAChB;IACA;QACE,aAAa;QACb,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,qBAAO,8OAAC;gBAAgB,MAAM,IAAI,QAAQ;;;;;;QAC5C;QACA,cAAc;IAChB;IACA;QACE,aAAa;QACb,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAChC,IAAI,QAAQ,CAAC,UAAU;;;;;;;;;;;IAIhC;IACA;QACE,aAAa;QACb,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;oBAChC,IAAI,QAAQ,CAAC,MAAM,KAAK,6BACvB,8OAAC,gPAAA,CAAA,wBAAqB;wBAAC,WAAU;;;;;6CAEjC,8OAAC,0NAAA,CAAA,aAAU;;;;;oBAEZ,IAAI,QAAQ,CAAC,MAAM;;;;;;;IAG1B;IACA;QACE,aAAa;QACb,QAAQ,kBAAM,8OAAC;gBAAI,WAAU;0BAAoB;;;;;;QACjD,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;gBAAI,WAAU;0BACZ,IAAI,KAAK,YAAY,CAAC,SAAS;oBAC9B,OAAO;oBACP,UAAU;gBACZ,GAAG,MAAM,CAAC,IAAI,QAAQ,CAAC,WAAW;;;;;;IAGxC;IACA;QACE,IAAI;QACJ,MAAM,kBACJ,8OAAC,qIAAA,CAAA,eAAY;;kCACX,8OAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,MAAK;;8CAEL,8OAAC,sOAAA,CAAA,mBAAgB;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAM,WAAU;;0CACzC,8OAAC,qIAAA,CAAA,mBAAgB;0CAAC;;;;;;0CAClB,8OAAC,qIAAA,CAAA,mBAAgB;0CAAC;;;;;;0CAClB,8OAAC,qIAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,SAAQ;0CAAc;;;;;;;;;;;;;;;;;;IAIhD;CACD;AAED,SAAS,aAAa,EAAE,GAAG,EAAwC;IACjE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QACpE,IAAI,IAAI,QAAQ,CAAC,EAAE;IACrB;IAEA,qBACE,8OAAC,0HAAA,CAAA,WAAQ;QACP,cAAY,IAAI,aAAa,MAAM;QACnC,iBAAe;QACf,KAAK;QACL,WAAU;QACV,OAAO;YACL,WAAW,qKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;YAClC,YAAY;QACd;kBAEC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,8OAAC,0HAAA,CAAA,YAAS;0BACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,UAAU;eADzC,KAAK,EAAE;;;;;;;;;;AAM/B;AAEO,SAAS,UAAU,EACxB,MAAM,WAAW,EAGlB;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,IAAM;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAmB,CAAC;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EACrD,EAAE;IAEJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;QACjD,WAAW;QACX,UAAU;IACZ;IACA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC7B,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,cAAW,EAAE,CAAC,IACxB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,cAAW,EAAE,CAAC,IACxB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,iBAAc,EAAE,CAAC;IAG7B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC1B,IAAM,MAAM,IAAI,CAAC,EAAE,EAAE,EAAE,GAAK,OAAO,EAAE,EACrC;QAAC;KAAK;IAGR,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;QACA,UAAU,CAAC,MAAQ,IAAI,EAAE,CAAC,QAAQ;QAClC,oBAAoB;QACpB,sBAAsB;QACtB,iBAAiB;QACjB,uBAAuB;QACvB,0BAA0B;QAC1B,oBAAoB;QACpB,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,oBAAoB,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;QACrC,wBAAwB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;IAC/C;IAEA,SAAS,cAAc,KAAmB;QACxC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QACzB,IAAI,UAAU,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YAC3C,QAAQ,CAAC;gBACP,MAAM,WAAW,QAAQ,OAAO,CAAC,OAAO,EAAE;gBAC1C,MAAM,WAAW,QAAQ,OAAO,CAAC,KAAK,EAAE;gBACxC,OAAO,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,UAAU;YACnC;QACF;IACF;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QACH,cAAa;QACb,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAgB,WAAU;kCAAU;;;;;;kCAGnD,8OAAC,2HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,8OAAC,2HAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,MAAK;gCACL,IAAG;0CAEH,cAAA,8OAAC,2HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kDACZ,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAmB;;;;;;kDACrC,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAgB;;;;;;kDAClC,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAkB;;;;;;;;;;;;;;;;;;kCAGxC,8OAAC,yHAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;;oCAAmB;kDACnB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;;;;;;;0CAE9C,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;;oCAAgB;kDACnB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;;;;;;;0CAE3C,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CAAkB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qIAAA,CAAA,eAAY;;kDACX,8OAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,wOAAA,CAAA,oBAAiB;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC,oOAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;kDAGpB,8OAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;kDACxC,MACE,aAAa,GACb,MAAM,CACL,CAAC,SACC,OAAO,OAAO,UAAU,KAAK,eAC7B,OAAO,UAAU,IAEpB,GAAG,CAAC,CAAC;4CACJ,qBACE,8OAAC,qIAAA,CAAA,2BAAwB;gDAEvB,WAAU;gDACV,SAAS,OAAO,YAAY;gDAC5B,iBAAiB,CAAC,QAChB,OAAO,gBAAgB,CAAC,CAAC,CAAC;0DAG3B,OAAO,EAAE;+CAPL,OAAO,EAAE;;;;;wCAUpB;;;;;;;;;;;;0CAGN,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,sNAAA,CAAA,WAAQ;;;;;kDACT,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;0BAIzC,8OAAC,yHAAA,CAAA,cAAW;gBACV,OAAM;gBACN,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2JAAA,CAAA,aAAU;4BACT,oBAAoB,2JAAA,CAAA,gBAAa;4BACjC,WAAW;gCAAC,qKAAA,CAAA,yBAAsB;6BAAC;4BACnC,WAAW;4BACX,SAAS;4BACT,IAAI;sCAEJ,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kDACJ,8OAAC,0HAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,8OAAC,0HAAA,CAAA,WAAQ;0DACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;oDACxB,qBACE,8OAAC,0HAAA,CAAA,YAAS;wDAAiB,SAAS,OAAO,OAAO;kEAC/C,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uDALT,OAAO,EAAE;;;;;gDAS7B;+CAZa,YAAY,EAAE;;;;;;;;;;kDAgBjC,8OAAC,0HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,WAAW,GAAG,IAAI,EAAE,uBACzB,8OAAC,mKAAA,CAAA,kBAAe;4CACd,OAAO;4CACP,UAAU,mKAAA,CAAA,8BAA2B;sDAEpC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC7B,8OAAC;oDAA0B,KAAK;mDAAb,IAAI,EAAE;;;;;;;;;iEAI7B,8OAAC,0HAAA,CAAA,WAAQ;sDACP,cAAA,8OAAC,0HAAA,CAAA,YAAS;gDACR,SAAS,QAAQ,MAAM;gDACvB,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;oCAAC;oCAAI;oCACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;oCAAC;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;0DAAsB;;;;;;0DAG/D,8OAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE;gDAChD,eAAe,CAAC;oDACd,MAAM,WAAW,CAAC,OAAO;gDAC3B;;kEAEA,8OAAC,2HAAA,CAAA,gBAAa;wDAAC,MAAK;wDAAK,WAAU;wDAAO,IAAG;kEAC3C,cAAA,8OAAC,2HAAA,CAAA,cAAW;4DACV,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;kEAGrD,8OAAC,2HAAA,CAAA,gBAAa;wDAAC,MAAK;kEACjB;4DAAC;4DAAI;4DAAI;4DAAI;4DAAI;yDAAG,CAAC,GAAG,CAAC,CAAC,yBACzB,8OAAC,2HAAA,CAAA,aAAU;gEAAgB,OAAO,GAAG,UAAU;0EAC5C;+DADc;;;;;;;;;;;;;;;;;;;;;;kDAOzB,8OAAC;wCAAI,WAAU;;4CAA6D;4CACpE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;4CAAE;4CAAI;4CACnD,MAAM,YAAY;;;;;;;kDAErB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,MAAM,YAAY,CAAC;gDAClC,UAAU,CAAC,MAAM,kBAAkB;;kEAEnC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC,sOAAA,CAAA,mBAAgB;;;;;;;;;;;0DAEnB,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,MAAM,YAAY;gDACjC,UAAU,CAAC,MAAM,kBAAkB;;kEAEnC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC,oOAAA,CAAA,kBAAe;;;;;;;;;;;0DAElB,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,MAAM,QAAQ;gDAC7B,UAAU,CAAC,MAAM,cAAc;;kEAE/B,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC,sOAAA,CAAA,mBAAgB;;;;;;;;;;;0DAEnB,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;gDACzD,UAAU,CAAC,MAAM,cAAc;;kEAE/B,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC,wOAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC,yHAAA,CAAA,cAAW;gBACV,OAAM;gBACN,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC,yHAAA,CAAA,cAAW;gBAAC,OAAM;gBAAgB,WAAU;0BAC3C,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC,yHAAA,CAAA,cAAW;gBACV,OAAM;gBACN,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;AAEA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAW,SAAS;QAAK,QAAQ;IAAG;IAC7C;QAAE,OAAO;QAAY,SAAS;QAAK,QAAQ;IAAI;IAC/C;QAAE,OAAO;QAAS,SAAS;QAAK,QAAQ;IAAI;IAC5C;QAAE,OAAO;QAAS,SAAS;QAAI,QAAQ;IAAI;IAC3C;QAAE,OAAO;QAAO,SAAS;QAAK,QAAQ;IAAI;IAC1C;QAAE,OAAO;QAAQ,SAAS;QAAK,QAAQ;IAAI;CAC5C;AAED,MAAM,cAAc;IAClB,SAAS;QACP,OAAO;QACP,OAAO;IACT;IACA,QAAQ;QACN,OAAO;QACP,OAAO;IACT;AACF;AAEA,SAAS,gBAAgB,EAAE,IAAI,EAAoC;IACjE,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,WAAW,WAAW,WAAW;;0BACvC,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAO,WAAU;8BAC9B,KAAK,UAAU;;;;;;;;;;;0BAGpB,8OAAC,2HAAA,CAAA,gBAAa;;kCACZ,8OAAC,2HAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,2HAAA,CAAA,cAAW;0CAAE,KAAK,UAAU;;;;;;0CAC7B,8OAAC,2HAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;4BACZ,CAAC,0BACA;;kDACE,8OAAC,0HAAA,CAAA,iBAAc;wCAAC,QAAQ;kDACtB,cAAA,8OAAC,qJAAA,CAAA,YAAS;4CACR,kBAAkB;4CAClB,MAAM;4CACN,QAAQ;gDACN,MAAM;gDACN,OAAO;4CACT;;8DAEA,8OAAC,6JAAA,CAAA,gBAAa;oDAAC,UAAU;;;;;;8DACzB,8OAAC,qJAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,UAAU;oDACV,UAAU;oDACV,YAAY;oDACZ,eAAe,CAAC,QAAU,MAAM,KAAK,CAAC,GAAG;oDACzC,IAAI;;;;;;8DAEN,8OAAC,0HAAA,CAAA,eAAY;oDACX,QAAQ;oDACR,uBAAS,8OAAC,0HAAA,CAAA,sBAAmB;wDAAC,WAAU;;;;;;;;;;;8DAE1C,8OAAC,oJAAA,CAAA,OAAI;oDACH,SAAQ;oDACR,MAAK;oDACL,MAAK;oDACL,aAAa;oDACb,QAAO;oDACP,SAAQ;;;;;;8DAEV,8OAAC,oJAAA,CAAA,OAAI;oDACH,SAAQ;oDACR,MAAK;oDACL,MAAK;oDACL,aAAa;oDACb,QAAO;oDACP,SAAQ;;;;;;;;;;;;;;;;;kDAId,8OAAC,8HAAA,CAAA,YAAS;;;;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAsC;oDACpB;kEAC/B,8OAAC,kOAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAMzC,8OAAC,8HAAA,CAAA,YAAS;;;;;;;0CAGd,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,8OAAC,0HAAA,CAAA,QAAK;gDAAC,IAAG;gDAAa,cAAc,KAAK,UAAU;;;;;;;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC,2HAAA,CAAA,SAAM;wDAAC,cAAc,KAAK,UAAU;;0EACnC,8OAAC,2HAAA,CAAA,gBAAa;gEAAC,IAAG;gEAAa,WAAU;0EACvC,cAAA,8OAAC,2HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kFACZ,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAkB;;;;;;kFAGpC,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAkB;;;;;;kFAGpC,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAc;;;;;;kFAGhC,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAiB;;;;;;kFAGnC,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS;;;;;;;;;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,8OAAC,2HAAA,CAAA,SAAM;wDAAC,cAAc,KAAK,MAAM;;0EAC/B,8OAAC,2HAAA,CAAA,gBAAa;gEAAC,IAAG;gEAAS,WAAU;0EACnC,cAAA,8OAAC,2HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kFACZ,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAa;;;;;;kFAC/B,8OAAC,2HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,cAAc,KAAK,WAAW;gDAC9B,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAKpB,8OAAC,2HAAA,CAAA,eAAY;;0CACX,8OAAC,2HAAA,CAAA,SAAM;0CAAC;;;;;;0CACR,8OAAC,2HAAA,CAAA,cAAW;gCAAC,OAAO;0CAClB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 5260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/lib/file-manager.ts"], "sourcesContent": ["export interface UploadedFile {\n  id: string;\n  name: string;\n  size: number;\n  content: any;\n  uploadedAt: Date;\n  type: 'json';\n}\n\nexport class FileManager {\n  private static readonly STORAGE_KEY = 'uploaded-files';\n\n  static saveFiles(files: UploadedFile[]): void {\n    try {\n      const serializedFiles = files.map(file => ({\n        ...file,\n        uploadedAt: file.uploadedAt.toISOString(),\n      }));\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(serializedFiles));\n    } catch (error) {\n      console.error('Error saving files to localStorage:', error);\n    }\n  }\n\n  static loadFiles(): UploadedFile[] {\n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      if (!stored) return [];\n      \n      const parsed = JSON.parse(stored);\n      return parsed.map((file: any) => ({\n        ...file,\n        uploadedAt: new Date(file.uploadedAt),\n      }));\n    } catch (error) {\n      console.error('Error loading files from localStorage:', error);\n      return [];\n    }\n  }\n\n  static addFile(file: UploadedFile): UploadedFile[] {\n    const existingFiles = this.loadFiles();\n    const updatedFiles = [...existingFiles, file];\n    this.saveFiles(updatedFiles);\n    return updatedFiles;\n  }\n\n  static removeFile(fileId: string): UploadedFile[] {\n    const existingFiles = this.loadFiles();\n    const updatedFiles = existingFiles.filter(file => file.id !== fileId);\n    this.saveFiles(updatedFiles);\n    return updatedFiles;\n  }\n\n  static clearAllFiles(): void {\n    localStorage.removeItem(this.STORAGE_KEY);\n  }\n\n  static getFileById(fileId: string): UploadedFile | null {\n    const files = this.loadFiles();\n    return files.find(file => file.id === fileId) || null;\n  }\n\n  static getFilesByType(type: string): UploadedFile[] {\n    const files = this.loadFiles();\n    return files.filter(file => file.type === type);\n  }\n}\n\n// Hook for React components\nexport function useFileManager() {\n  const saveFiles = (files: UploadedFile[]) => FileManager.saveFiles(files);\n  const loadFiles = () => FileManager.loadFiles();\n  const addFile = (file: UploadedFile) => FileManager.addFile(file);\n  const removeFile = (fileId: string) => FileManager.removeFile(fileId);\n  const clearAllFiles = () => FileManager.clearAllFiles();\n  const getFileById = (fileId: string) => FileManager.getFileById(fileId);\n  const getFilesByType = (type: string) => FileManager.getFilesByType(type);\n\n  return {\n    saveFiles,\n    loadFiles,\n    addFile,\n    removeFile,\n    clearAllFiles,\n    getFileById,\n    getFilesByType,\n  };\n}\n"], "names": [], "mappings": ";;;;AASO,MAAM;IACX,OAAwB,cAAc,iBAAiB;IAEvD,OAAO,UAAU,KAAqB,EAAQ;QAC5C,IAAI;YACF,MAAM,kBAAkB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACzC,GAAG,IAAI;oBACP,YAAY,KAAK,UAAU,CAAC,WAAW;gBACzC,CAAC;YACD,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,OAAO,YAA4B;QACjC,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO,OAAO,GAAG,CAAC,CAAC,OAAc,CAAC;oBAChC,GAAG,IAAI;oBACP,YAAY,IAAI,KAAK,KAAK,UAAU;gBACtC,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO,EAAE;QACX;IACF;IAEA,OAAO,QAAQ,IAAkB,EAAkB;QACjD,MAAM,gBAAgB,IAAI,CAAC,SAAS;QACpC,MAAM,eAAe;eAAI;YAAe;SAAK;QAC7C,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,OAAO,WAAW,MAAc,EAAkB;QAChD,MAAM,gBAAgB,IAAI,CAAC,SAAS;QACpC,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC9D,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,OAAO,gBAAsB;QAC3B,aAAa,UAAU,CAAC,IAAI,CAAC,WAAW;IAC1C;IAEA,OAAO,YAAY,MAAc,EAAuB;QACtD,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,WAAW;IACnD;IAEA,OAAO,eAAe,IAAY,EAAkB;QAClD,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAC5C;AACF;AAGO,SAAS;IACd,MAAM,YAAY,CAAC,QAA0B,YAAY,SAAS,CAAC;IACnE,MAAM,YAAY,IAAM,YAAY,SAAS;IAC7C,MAAM,UAAU,CAAC,OAAuB,YAAY,OAAO,CAAC;IAC5D,MAAM,aAAa,CAAC,SAAmB,YAAY,UAAU,CAAC;IAC9D,MAAM,gBAAgB,IAAM,YAAY,aAAa;IACrD,MAAM,cAAc,CAAC,SAAmB,YAAY,WAAW,CAAC;IAChE,MAAM,iBAAiB,CAAC,OAAiB,YAAY,cAAc,CAAC;IAEpE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5342, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/file-upload.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useCallback, useState, useEffect } from \"react\";\nimport { useDropzone } from \"react-dropzone\";\nimport { IconUpload, IconFile, IconX, IconCheck } from \"@tabler/icons-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { UploadedFile, useFileManager } from \"@/lib/file-manager\";\n\ninterface FileUploadProps {\n  onFileUpload?: (files: UploadedFile[]) => void;\n  maxFiles?: number;\n  acceptedFileTypes?: string[];\n  className?: string;\n}\n\nexport function FileUpload({\n  onFileUpload,\n  maxFiles = 5,\n  acceptedFileTypes = ['.json'],\n  className = \"\"\n}: FileUploadProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isUploading, setIsUploading] = useState(false);\n  const { loadFiles, addFile, removeFile } = useFileManager();\n\n  useEffect(() => {\n    const files = loadFiles();\n    setUploadedFiles(files);\n    onFileUpload?.(files);\n  }, []);\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    setIsUploading(true);\n    \n    try {\n      const newFiles: UploadedFile[] = [];\n      \n      for (const file of acceptedFiles) {\n        if (file.type === 'application/json' || file.name.endsWith('.json')) {\n          const content = await file.text();\n          try {\n            const parsedContent = JSON.parse(content);\n            const uploadedFile: UploadedFile = {\n              id: Math.random().toString(36).substr(2, 9),\n              name: file.name,\n              size: file.size,\n              content: parsedContent,\n              uploadedAt: new Date(),\n              type: 'json',\n            };\n            newFiles.push(uploadedFile);\n          } catch (error) {\n            console.error(`Error parsing JSON file ${file.name}:`, error);\n          }\n        }\n      }\n      \n      // Add files to persistent storage\n      for (const file of newFiles) {\n        addFile(file);\n      }\n\n      const updatedFiles = loadFiles().slice(0, maxFiles);\n      setUploadedFiles(updatedFiles);\n      onFileUpload?.(updatedFiles);\n    } catch (error) {\n      console.error('Error processing files:', error);\n    } finally {\n      setIsUploading(false);\n    }\n  }, [uploadedFiles, maxFiles, onFileUpload]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/json': acceptedFileTypes,\n    },\n    maxFiles,\n    disabled: isUploading,\n  });\n\n  const handleRemoveFile = (fileId: string) => {\n    const updatedFiles = removeFile(fileId);\n    setUploadedFiles(updatedFiles);\n    onFileUpload?.(updatedFiles);\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <Card>\n        <CardContent className=\"p-6\">\n          <div\n            {...getRootProps()}\n            className={`\n              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n              ${isDragActive \n                ? 'border-primary bg-primary/5' \n                : 'border-muted-foreground/25 hover:border-muted-foreground/50'\n              }\n              ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}\n            `}\n          >\n            <input {...getInputProps()} />\n            <IconUpload className=\"mx-auto h-12 w-12 text-muted-foreground mb-4\" />\n            <div className=\"space-y-2\">\n              <p className=\"text-lg font-medium\">\n                {isDragActive ? 'Drop JSON files here' : 'Upload JSON files'}\n              </p>\n              <p className=\"text-sm text-muted-foreground\">\n                Drag and drop JSON files here, or click to select files\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                Maximum {maxFiles} files • JSON format only\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {uploadedFiles.length > 0 && (\n        <Card>\n          <CardContent className=\"p-4\">\n            <h3 className=\"font-medium mb-3\">Uploaded Files</h3>\n            <div className=\"space-y-2\">\n              {uploadedFiles.map((file) => (\n                <div\n                  key={file.id}\n                  className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <IconFile className=\"h-5 w-5 text-muted-foreground\" />\n                    <div>\n                      <p className=\"text-sm font-medium\">{file.name}</p>\n                      <p className=\"text-xs text-muted-foreground\">\n                        {formatFileSize(file.size)} • {file.uploadedAt.toLocaleTimeString()}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      <IconCheck className=\"h-3 w-3 mr-1\" />\n                      JSON\n                    </Badge>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => handleRemoveFile(file.id)}\n                      className=\"h-8 w-8 p-0\"\n                    >\n                      <IconX className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAiBO,SAAS,WAAW,EACzB,YAAY,EACZ,WAAW,CAAC,EACZ,oBAAoB;IAAC;CAAQ,EAC7B,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ;QACd,iBAAiB;QACjB,eAAe;IACjB,GAAG,EAAE;IAEL,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,eAAe;QAEf,IAAI;YACF,MAAM,WAA2B,EAAE;YAEnC,KAAK,MAAM,QAAQ,cAAe;gBAChC,IAAI,KAAK,IAAI,KAAK,sBAAsB,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;oBACnE,MAAM,UAAU,MAAM,KAAK,IAAI;oBAC/B,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC;wBACjC,MAAM,eAA6B;4BACjC,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;4BACzC,MAAM,KAAK,IAAI;4BACf,MAAM,KAAK,IAAI;4BACf,SAAS;4BACT,YAAY,IAAI;4BAChB,MAAM;wBACR;wBACA,SAAS,IAAI,CAAC;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;oBACzD;gBACF;YACF;YAEA,kCAAkC;YAClC,KAAK,MAAM,QAAQ,SAAU;gBAC3B,QAAQ;YACV;YAEA,MAAM,eAAe,YAAY,KAAK,CAAC,GAAG;YAC1C,iBAAiB;YACjB,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAe;QAAU;KAAa;IAE1C,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,oBAAoB;QACtB;QACA;QACA,UAAU;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,WAAW;QAChC,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;SAAK;QACnC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBACE,GAAG,cAAc;wBAClB,WAAW,CAAC;;cAEV,EAAE,eACE,gCACA,8DACH;cACD,EAAE,cAAc,kCAAkC,GAAG;YACvD,CAAC;;0CAED,8OAAC;gCAAO,GAAG,eAAe;;;;;;0CAC1B,8OAAC,0NAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,eAAe,yBAAyB;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,8OAAC;wCAAE,WAAU;;4CAAgC;4CAClC;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO3B,cAAc,MAAM,GAAG,mBACtB,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCAAmB;;;;;;sCACjC,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAuB,KAAK,IAAI;;;;;;sEAC7C,8OAAC;4DAAE,WAAU;;gEACV,eAAe,KAAK,IAAI;gEAAE;gEAAI,KAAK,UAAU,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;sDAIvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;sEACnC,8OAAC,wNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGxC,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB,KAAK,EAAE;oDACvC,WAAU;8DAEV,cAAA,8OAAC,gNAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;mCAvBhB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkC9B", "debugId": null}}, {"offset": {"line": 5662, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5727, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/hooks/use-investment-data.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\n\ninterface InvestmentData {\n  _id?: string;\n  instrument: string;\n  assetClass: string;\n  outstanding?: number;\n  commitment?: number;\n  called?: number;\n  status?: string;\n  year?: number;\n  [key: string]: any;\n}\n\ninterface InvestmentSummary {\n  totalOutstanding: number;\n  totalCommitment: number;\n  totalCalled: number;\n  assetClasses: string[];\n  yearlyBreakdown: Record<number, number>;\n  statusBreakdown: Record<string, number>;\n  totalInstruments: number;\n}\n\ninterface UseInvestmentDataReturn {\n  data: InvestmentData[];\n  summary: InvestmentSummary | null;\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  uploadData: (data: InvestmentData[], replace?: boolean) => Promise<boolean>;\n}\n\ninterface UseInvestmentDataOptions {\n  assetClass?: string;\n  status?: string;\n  year?: number;\n  autoFetch?: boolean;\n}\n\nexport function useInvestmentData(options: UseInvestmentDataOptions = {}): UseInvestmentDataReturn {\n  const [data, setData] = useState<InvestmentData[]>([]);\n  const [summary, setSummary] = useState<InvestmentSummary | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchData = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const params = new URLSearchParams();\n      if (options.assetClass) params.append('assetClass', options.assetClass);\n      if (options.status) params.append('status', options.status);\n      if (options.year) params.append('year', options.year.toString());\n\n      const response = await fetch(`/api/investments?${params.toString()}`);\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || 'Failed to fetch data');\n      }\n\n      setData(result.data || []);\n      setSummary(result.processed || null);\n      \n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';\n      setError(errorMessage);\n      console.error('Error fetching investment data:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [options.assetClass, options.status, options.year]);\n\n  const uploadData = useCallback(async (newData: InvestmentData[], replace = false): Promise<boolean> => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/investments', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          data: newData,\n          replace\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || 'Failed to upload data');\n      }\n\n      // Refetch data after successful upload\n      await fetchData();\n      return true;\n      \n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';\n      setError(errorMessage);\n      console.error('Error uploading investment data:', err);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchData]);\n\n  useEffect(() => {\n    if (options.autoFetch !== false) {\n      fetchData();\n    }\n  }, [fetchData, options.autoFetch]);\n\n  return {\n    data,\n    summary,\n    loading,\n    error,\n    refetch: fetchData,\n    uploadData\n  };\n}\n\n// Hook for testing MongoDB connection\nexport function useMongoConnection() {\n  const [connected, setConnected] = useState<boolean | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [info, setInfo] = useState<any>(null);\n\n  const testConnection = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/test-connection');\n      const result = await response.json();\n      \n      setConnected(result.success);\n      setInfo(result);\n      \n      if (!result.success) {\n        setError(result.error || 'Connection failed');\n      }\n      \n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';\n      setError(errorMessage);\n      setConnected(false);\n      console.error('Error testing MongoDB connection:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    testConnection();\n  }, [testConnection]);\n\n  return {\n    connected,\n    loading,\n    error,\n    info,\n    testConnection\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAwCO,SAAS,kBAAkB,UAAoC,CAAC,CAAC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,UAAU,EAAE,OAAO,MAAM,CAAC,cAAc,QAAQ,UAAU;YACtE,IAAI,QAAQ,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC1D,IAAI,QAAQ,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;YAE7D,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,OAAO,QAAQ,IAAI;YAEpE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,QAAQ,OAAO,IAAI,IAAI,EAAE;YACzB,WAAW,OAAO,SAAS,IAAI;QAEjC,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC,QAAQ,UAAU;QAAE,QAAQ,MAAM;QAAE,QAAQ,IAAI;KAAC;IAErD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAA2B,UAAU,KAAK;QAC9E,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,uCAAuC;YACvC,MAAM;YACN,OAAO;QAET,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,SAAS,KAAK,OAAO;YAC/B;QACF;IACF,GAAG;QAAC;QAAW,QAAQ,SAAS;KAAC;IAEjC,OAAO;QACL;QACA;QACA;QACA;QACA,SAAS;QACT;IACF;AACF;AAGO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEtC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,aAAa,OAAO,OAAO;YAC3B,QAAQ;YAER,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,SAAS,OAAO,KAAK,IAAI;YAC3B;QAEF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,aAAa;YACb,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5864, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/dashboard-with-mongodb.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { ChartAreaInteractive } from \"@/components/chart-area-interactive\";\nimport { DataTable } from \"@/components/data-table\";\nimport { FileUpload } from \"@/components/file-upload\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { IconUpload, IconDatabase, IconRefresh, IconAlertCircle, IconCheck } from \"@tabler/icons-react\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { useInvestmentData, useMongoConnection } from \"@/hooks/use-investment-data\";\n\n// No static data imports - using MongoDB only\n\ninterface UploadedFile {\n  id: string;\n  name: string;\n  size: number;\n  content: any;\n  uploadedAt: Date;\n}\n\nexport function DashboardWithMongoDB() {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [showFileUpload, setShowFileUpload] = useState(false);\n  const [useMongoData, setUseMongoData] = useState(true);\n  \n  // MongoDB hooks\n  const { \n    data: mongoData, \n    summary, \n    loading: dataLoading, \n    error: dataError, \n    refetch,\n    uploadData \n  } = useInvestmentData({ autoFetch: true });\n  \n  const { \n    connected, \n    loading: connectionLoading, \n    error: connectionError, \n    info: connectionInfo,\n    testConnection \n  } = useMongoConnection();\n\n  // Determine which data to use - MongoDB only now\n  const currentData = useMongoData && mongoData.length > 0 ? mongoData : [];\n  const hasMongoData = mongoData.length > 0;\n\n  const handleFileUpload = async (files: UploadedFile[]) => {\n    setUploadedFiles(files);\n    \n    if (files.length > 0 && useMongoData) {\n      // Process and upload files to MongoDB\n      const allData: any[] = [];\n      \n      files.forEach(file => {\n        if (Array.isArray(file.content)) {\n          allData.push(...file.content);\n        } else {\n          allData.push(file.content);\n        }\n      });\n\n      // Transform data to match expected format\n      const transformedData = allData.map((item, index) => ({\n        instrument: item.instrument || item.Instrument || `Instrument ${index + 1}`,\n        assetClass: item.assetClass || item[\"Asset Class\"] || \"Unknown\",\n        outstanding: parseFloat(item.outstanding || item[\"Outstanding Commitment\"] || 0),\n        commitment: parseFloat(item.commitment || item[\"Capital Commitment\"] || 0),\n        called: parseFloat(item.called || item[\"Paid Commitment\"] || 0),\n        status: item.status || \"Active\",\n        year: item.year || new Date().getFullYear(),\n        ...item\n      }));\n\n      const success = await uploadData(transformedData, true);\n      if (success) {\n        console.log('Data uploaded to MongoDB successfully');\n      }\n    }\n  };\n\n  const resetToDefaultData = () => {\n    setUploadedFiles([]);\n    setUseMongoData(false);\n  };\n\n  const switchToMongoData = () => {\n    setUseMongoData(true);\n    refetch();\n  };\n\n  return (\n    <div className=\"flex flex-1 flex-col\">\n      <div className=\"@container/main flex flex-1 flex-col gap-2\">\n        <div className=\"flex flex-col gap-4 py-4 md:gap-6 md:py-6\">\n          \n          {/* MongoDB Connection Status */}\n          <div className=\"px-4 lg:px-6\">\n            <Alert className={connected ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\"}>\n              <div className=\"flex items-center gap-2\">\n                {connectionLoading ? (\n                  <IconRefresh className=\"h-4 w-4 animate-spin\" />\n                ) : connected ? (\n                  <IconCheck className=\"h-4 w-4 text-green-600\" />\n                ) : (\n                  <IconAlertCircle className=\"h-4 w-4 text-red-600\" />\n                )}\n                <AlertDescription>\n                  {connectionLoading ? (\n                    \"Testing MongoDB connection...\"\n                  ) : connected ? (\n                    <div className=\"flex items-center gap-4\">\n                      <span>MongoDB connected successfully</span>\n                      {connectionInfo && (\n                        <div className=\"flex gap-2\">\n                          <Badge variant=\"outline\">\n                            DB: {connectionInfo.database}\n                          </Badge>\n                          <Badge variant=\"outline\">\n                            Collections: {connectionInfo.collections?.length || 0}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  ) : (\n                    <div>\n                      MongoDB connection failed: {connectionError}\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\" \n                        className=\"ml-2\"\n                        onClick={testConnection}\n                      >\n                        Retry\n                      </Button>\n                    </div>\n                  )}\n                </AlertDescription>\n              </div>\n            </Alert>\n          </div>\n\n          {/* Data Source Controls */}\n          <div className=\"px-4 lg:px-6\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <IconDatabase className=\"h-5 w-5\" />\n                    <CardTitle>Data Source</CardTitle>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <Badge variant={useMongoData ? \"default\" : \"secondary\"}>\n                      {useMongoData ? \"MongoDB\" : \"Static Data\"}\n                    </Badge>\n                    {hasMongoData && (\n                      <Badge variant=\"outline\">\n                        {mongoData.length} records\n                      </Badge>\n                    )}\n                  </div>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex flex-wrap gap-2\">\n                  {connected && (\n                    <Button\n                      variant={useMongoData ? \"default\" : \"outline\"}\n                      size=\"sm\"\n                      onClick={switchToMongoData}\n                      disabled={dataLoading}\n                    >\n                      {dataLoading && <IconRefresh className=\"h-4 w-4 mr-2 animate-spin\" />}\n                      Use MongoDB Data\n                    </Button>\n                  )}\n                  <Button\n                    variant={!useMongoData ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={resetToDefaultData}\n                  >\n                    Use Static Data\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={refetch}\n                    disabled={dataLoading || !connected}\n                  >\n                    <IconRefresh className=\"h-4 w-4 mr-2\" />\n                    Refresh\n                  </Button>\n                </div>\n                \n                {dataError && (\n                  <Alert className=\"mt-4 border-red-200 bg-red-50\">\n                    <IconAlertCircle className=\"h-4 w-4 text-red-600\" />\n                    <AlertDescription>\n                      Error loading data: {dataError}\n                    </AlertDescription>\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* File Upload Section */}\n          <div className=\"px-4 lg:px-6\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <IconUpload className=\"h-5 w-5\" />\n                    <CardTitle>Upload Data to MongoDB</CardTitle>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    {uploadedFiles.length > 0 && (\n                      <Badge variant=\"secondary\">\n                        {uploadedFiles.length} file(s) uploaded\n                      </Badge>\n                    )}\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => setShowFileUpload(!showFileUpload)}\n                      disabled={!connected}\n                    >\n                      <IconUpload className=\"h-4 w-4 mr-2\" />\n                      {showFileUpload ? 'Hide Upload' : 'Upload Data'}\n                    </Button>\n                  </div>\n                </div>\n              </CardHeader>\n              {showFileUpload && (\n                <CardContent>\n                  <FileUpload\n                    onFileUpload={handleFileUpload}\n                    maxFiles={3}\n                  />\n                  <div className=\"mt-4 p-4 bg-muted/50 rounded-lg\">\n                    <h4 className=\"text-sm font-medium mb-2\">Upload to MongoDB:</h4>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Upload JSON files with investment data. The data will be automatically \n                      processed and stored in your MongoDB collection, then displayed in the dashboard.\n                    </p>\n                  </div>\n                </CardContent>\n              )}\n            </Card>\n          </div>\n\n          {/* Summary Cards */}\n          {summary && (\n            <div className=\"px-4 lg:px-6\">\n              <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n                <Card>\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium\">Total Outstanding</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold\">\n                      ${summary.totalOutstanding.toLocaleString()}\n                    </div>\n                  </CardContent>\n                </Card>\n                <Card>\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium\">Total Commitment</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold\">\n                      ${summary.totalCommitment.toLocaleString()}\n                    </div>\n                  </CardContent>\n                </Card>\n                <Card>\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium\">Total Called</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold\">\n                      ${summary.totalCalled.toLocaleString()}\n                    </div>\n                  </CardContent>\n                </Card>\n                <Card>\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium\">Instruments</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold\">\n                      {summary.totalInstruments}\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          )}\n\n          {/* Chart Section */}\n          <div className=\"px-4 lg:px-6\">\n            <ChartAreaInteractive />\n          </div>\n\n          {/* Data Table Section */}\n          <DataTable data={currentData} />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,MAAM,EACJ,MAAM,SAAS,EACf,OAAO,EACP,SAAS,WAAW,EACpB,OAAO,SAAS,EAChB,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE,WAAW;IAAK;IAExC,MAAM,EACJ,SAAS,EACT,SAAS,iBAAiB,EAC1B,OAAO,eAAe,EACtB,MAAM,cAAc,EACpB,cAAc,EACf,GAAG,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;IAErB,iDAAiD;IACjD,MAAM,cAAc,gBAAgB,UAAU,MAAM,GAAG,IAAI,YAAY,EAAE;IACzE,MAAM,eAAe,UAAU,MAAM,GAAG;IAExC,MAAM,mBAAmB,OAAO;QAC9B,iBAAiB;QAEjB,IAAI,MAAM,MAAM,GAAG,KAAK,cAAc;YACpC,sCAAsC;YACtC,MAAM,UAAiB,EAAE;YAEzB,MAAM,OAAO,CAAC,CAAA;gBACZ,IAAI,MAAM,OAAO,CAAC,KAAK,OAAO,GAAG;oBAC/B,QAAQ,IAAI,IAAI,KAAK,OAAO;gBAC9B,OAAO;oBACL,QAAQ,IAAI,CAAC,KAAK,OAAO;gBAC3B;YACF;YAEA,0CAA0C;YAC1C,MAAM,kBAAkB,QAAQ,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBACpD,YAAY,KAAK,UAAU,IAAI,KAAK,UAAU,IAAI,CAAC,WAAW,EAAE,QAAQ,GAAG;oBAC3E,YAAY,KAAK,UAAU,IAAI,IAAI,CAAC,cAAc,IAAI;oBACtD,aAAa,WAAW,KAAK,WAAW,IAAI,IAAI,CAAC,yBAAyB,IAAI;oBAC9E,YAAY,WAAW,KAAK,UAAU,IAAI,IAAI,CAAC,qBAAqB,IAAI;oBACxE,QAAQ,WAAW,KAAK,MAAM,IAAI,IAAI,CAAC,kBAAkB,IAAI;oBAC7D,QAAQ,KAAK,MAAM,IAAI;oBACvB,MAAM,KAAK,IAAI,IAAI,IAAI,OAAO,WAAW;oBACzC,GAAG,IAAI;gBACT,CAAC;YAED,MAAM,UAAU,MAAM,WAAW,iBAAiB;YAClD,IAAI,SAAS;gBACX,QAAQ,GAAG,CAAC;YACd;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,iBAAiB,EAAE;QACnB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB;QACxB,gBAAgB;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;4BAAC,WAAW,YAAY,iCAAiC;sCAC7D,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,kCACC,8OAAC,4NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;+CACrB,0BACF,8OAAC,wNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,oOAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAE7B,8OAAC,0HAAA,CAAA,mBAAgB;kDACd,oBACC,kCACE,0BACF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;gDACL,gCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAU;gEAClB,eAAe,QAAQ;;;;;;;sEAE9B,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAU;gEACT,eAAe,WAAW,EAAE,UAAU;;;;;;;;;;;;;;;;;;iEAM5D,8OAAC;;gDAAI;gDACyB;8DAC5B,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;;8CACH,8OAAC,yHAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC,yHAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAS,eAAe,YAAY;kEACxC,eAAe,YAAY;;;;;;oDAE7B,8BACC,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,UAAU,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC,yHAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;;gDACZ,2BACC,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,eAAe,YAAY;oDACpC,MAAK;oDACL,SAAS;oDACT,UAAU;;wDAET,6BAAe,8OAAC,4NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;8DAI1E,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,CAAC,eAAe,YAAY;oDACrC,MAAK;oDACL,SAAS;8DACV;;;;;;8DAGD,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,UAAU,eAAe,CAAC;;sEAE1B,8OAAC,4NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;wCAK3C,2BACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,oOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,8OAAC,0HAAA,CAAA,mBAAgB;;wDAAC;wDACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;;8CACH,8OAAC,yHAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0NAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC,yHAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,8OAAC;gDAAI,WAAU;;oDACZ,cAAc,MAAM,GAAG,mBACtB,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,cAAc,MAAM;4DAAC;;;;;;;kEAG1B,8OAAC,2HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,kBAAkB,CAAC;wDAClC,UAAU,CAAC;;0EAEX,8OAAC,0NAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DACrB,iBAAiB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;gCAKzC,gCACC,8OAAC,yHAAA,CAAA,cAAW;;sDACV,8OAAC,6HAAA,CAAA,aAAU;4CACT,cAAc;4CACd,UAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWtD,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;oDAAqB;oDAChC,QAAQ,gBAAgB,CAAC,cAAc;;;;;;;;;;;;;;;;;;8CAI/C,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;oDAAqB;oDAChC,QAAQ,eAAe,CAAC,cAAc;;;;;;;;;;;;;;;;;;8CAI9C,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;oDAAqB;oDAChC,QAAQ,WAAW,CAAC,cAAc;;;;;;;;;;;;;;;;;;8CAI1C,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2IAAA,CAAA,uBAAoB;;;;;;;;;;kCAIvB,8OAAC,4HAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 6588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/theme-selector.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useThemeConfig } from \"./active-theme\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\n\nconst DEFAULT_THEMES = [\n  {\n    name: \"Default\",\n    value: \"default\",\n  },\n  {\n    name: \"Blue\",\n    value: \"blue\",\n  },\n  {\n    name: \"Green\",\n    value: \"green\",\n  },\n  {\n    name: \"Amber\",\n    value: \"amber\",\n  },\n];\n\nconst SCALED_THEMES = [\n  {\n    name: \"Default\",\n    value: \"default-scaled\",\n  },\n  {\n    name: \"Blue\",\n    value: \"blue-scaled\",\n  },\n];\n\nconst MONO_THEMES = [\n  {\n    name: \"Mono\",\n    value: \"mono-scaled\",\n  },\n];\n\nexport function ThemeSelector() {\n  const { activeTheme, setActiveTheme } = useThemeConfig();\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Label htmlFor=\"theme-selector\" className=\"sr-only\">\n        Theme\n      </Label>\n      <Select value={activeTheme} onValueChange={setActiveTheme}>\n        <SelectTrigger\n          id=\"theme-selector\"\n          size=\"sm\"\n          className=\"justify-start *:data-[slot=select-value]:w-12\"\n        >\n          <span className=\"text-muted-foreground hidden sm:block\">\n            Select a theme:\n          </span>\n          <span className=\"text-muted-foreground block sm:hidden\">Theme</span>\n          <SelectValue placeholder=\"Select a theme\" />\n        </SelectTrigger>\n        <SelectContent align=\"end\">\n          <SelectGroup>\n            <SelectLabel>Default</SelectLabel>\n            {DEFAULT_THEMES.map((theme) => (\n              <SelectItem key={theme.name} value={theme.value}>\n                {theme.name}\n              </SelectItem>\n            ))}\n          </SelectGroup>\n          <SelectSeparator />\n          <SelectGroup>\n            <SelectLabel>Scaled</SelectLabel>\n            {SCALED_THEMES.map((theme) => (\n              <SelectItem key={theme.name} value={theme.value}>\n                {theme.name}\n              </SelectItem>\n            ))}\n          </SelectGroup>\n          <SelectGroup>\n            <SelectLabel>Monospaced</SelectLabel>\n            {MONO_THEMES.map((theme) => (\n              <SelectItem key={theme.name} value={theme.value}>\n                {theme.name}\n              </SelectItem>\n            ))}\n          </SelectGroup>\n        </SelectContent>\n      </Select>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,iBAAiB;IACrB;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAiB,WAAU;0BAAU;;;;;;0BAGpD,8OAAC,2HAAA,CAAA,SAAM;gBAAC,OAAO;gBAAa,eAAe;;kCACzC,8OAAC,2HAAA,CAAA,gBAAa;wBACZ,IAAG;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAwC;;;;;;0CAGxD,8OAAC;gCAAK,WAAU;0CAAwC;;;;;;0CACxD,8OAAC,2HAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;;kCAE3B,8OAAC,2HAAA,CAAA,gBAAa;wBAAC,OAAM;;0CACnB,8OAAC,2HAAA,CAAA,cAAW;;kDACV,8OAAC,2HAAA,CAAA,cAAW;kDAAC;;;;;;oCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,2HAAA,CAAA,aAAU;4CAAkB,OAAO,MAAM,KAAK;sDAC5C,MAAM,IAAI;2CADI,MAAM,IAAI;;;;;;;;;;;0CAK/B,8OAAC,2HAAA,CAAA,kBAAe;;;;;0CAChB,8OAAC,2HAAA,CAAA,cAAW;;kDACV,8OAAC,2HAAA,CAAA,cAAW;kDAAC;;;;;;oCACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC,2HAAA,CAAA,aAAU;4CAAkB,OAAO,MAAM,KAAK;sDAC5C,MAAM,IAAI;2CADI,MAAM,IAAI;;;;;;;;;;;0CAK/B,8OAAC,2HAAA,CAAA,cAAW;;kDACV,8OAAC,2HAAA,CAAA,cAAW;kDAAC;;;;;;oCACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,8OAAC,2HAAA,CAAA,aAAU;4CAAkB,OAAO,MAAM,KAAK;sDAC5C,MAAM,IAAI;2CADI,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC", "debugId": null}}, {"offset": {"line": 6788, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/YadCap/components/mode-switcher.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { MoonIcon, SunIcon } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\n\nimport { Button } from \"@/components/ui/button\";\n\nexport function ModeSwitcher() {\n  const { setTheme, resolvedTheme } = useTheme();\n\n  const toggleTheme = React.useCallback(() => {\n    setTheme(resolvedTheme === \"dark\" ? \"light\" : \"dark\");\n  }, [resolvedTheme, setTheme]);\n\n  return (\n    <Button\n      variant=\"ghost\"\n      className=\"group/toggle h-8 w-8 px-0\"\n      onClick={toggleTheme}\n    >\n      <SunIcon className=\"hidden [html.dark_&]:block\" />\n      <MoonIcon className=\"hidden [html.light_&]:block\" />\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AANA;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACpC,SAAS,kBAAkB,SAAS,UAAU;IAChD,GAAG;QAAC;QAAe;KAAS;IAE5B,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,SAAQ;QACR,WAAU;QACV,SAAS;;0BAET,8OAAC,oMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,8OAAC,sMAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}]}