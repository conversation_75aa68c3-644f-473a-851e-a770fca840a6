module.exports = {

"[project]/.next-internal/server/app/api/investments/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Investment": (()=>Investment),
    "MDE": (()=>MDE),
    "MongoDBService": (()=>MongoDBService),
    "default": (()=>__TURBOPACK__default__export__),
    "getConnectionInfo": (()=>getConnectionInfo),
    "testConnection": (()=>testConnection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
if (!process.env.MONGODB_URI) {
    throw new Error('Invalid/Missing environment variable: "MONGODB_URI"');
}
const MONGODB_URI = process.env.MONGODB_URI;
console.log('MongoDB URI loaded:', MONGODB_URI ? 'URI found' : 'URI missing');
// Cache the database connection in development to prevent multiple connections
let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false,
            serverSelectionTimeoutMS: 10000,
            socketTimeoutMS: 45000,
            maxPoolSize: 10,
            minPoolSize: 5
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts);
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
async function testConnection() {
    try {
        await connectDB();
        return __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connection.readyState === 1;
    } catch (error) {
        console.error('MongoDB connection failed:', error);
        return false;
    }
}
async function getConnectionInfo() {
    try {
        await connectDB();
        const db = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connection.db;
        const collections = await db.listCollections().toArray();
        return {
            connected: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connection.readyState === 1,
            database: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connection.name,
            collections: collections.map((col)=>col.name),
            host: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connection.host,
            port: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connection.port
        };
    } catch (error) {
        console.error('Error getting connection info:', error);
        return {
            connected: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
// MDE Schema Definition - flexible schema to match actual data
const mdeSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({}, {
    strict: false,
    collection: 'MDE'
});
const MDE = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.MDE || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('MDE', mdeSchema);
const Investment = MDE;
class MongoDBService {
    static async create() {
        await connectDB();
        return new MongoDBService();
    }
    async findAll(modelName, filter = {}) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.find(filter).lean();
    }
    async findOne(modelName, filter) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.findOne(filter).lean();
    }
    async insertOne(modelName, document) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        const instance = new Model(document);
        return await instance.save();
    }
    async insertMany(modelName, documents) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.insertMany(documents);
    }
    async updateOne(modelName, filter, update) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.updateOne(filter, {
            $set: {
                ...update,
                updatedAt: new Date()
            }
        });
    }
    async deleteOne(modelName, filter) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.deleteOne(filter);
    }
    async deleteMany(modelName, filter) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.deleteMany(filter);
    }
    async count(modelName, filter = {}) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.countDocuments(filter);
    }
    async aggregate(modelName, pipeline) {
        await connectDB();
        const Model = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models[modelName];
        if (!Model) {
            throw new Error(`Model ${modelName} not found`);
        }
        return await Model.aggregate(pipeline);
    }
}
}}),
"[project]/app/api/investments/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/mongodb.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        // Ensure database connection
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { searchParams } = new URL(request.url);
        const assetClass = searchParams.get('assetClass');
        const status = searchParams.get('status');
        const year = searchParams.get('year');
        // Build filter based on query parameters
        const filter = {};
        if (assetClass && assetClass !== 'all') {
            filter.assetClass = assetClass;
        }
        if (status) {
            filter.status = status;
        }
        if (year) {
            filter.year = parseInt(year);
        }
        const data = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Investment"].find(filter).lean();
        // Process data for dashboard consumption
        const processedData = processInvestmentData(data);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: processedData.raw,
            processed: processedData.summary,
            total: data.length,
            filter
        });
    } catch (error) {
        console.error('Investments API Error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch investment data',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        // Ensure database connection
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const body = await request.json();
        const { data, replace = false } = body;
        if (!data || !Array.isArray(data)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Data array is required'
            }, {
                status: 400
            });
        }
        // If replace is true, clear existing data first
        if (replace) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Investment"].deleteMany({});
        }
        // Validate and process the data
        const validatedData = data.map((item)=>({
                instrument: item.instrument || item.name || 'Unknown',
                assetClass: item.assetClass || item.asset_class || 'Unknown',
                outstanding: parseFloat(item.outstanding || item.amount || 0),
                commitment: parseFloat(item.commitment || 0),
                called: parseFloat(item.called || 0),
                status: item.status || 'Active',
                year: item.year || new Date().getFullYear(),
                ...item
            }));
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Investment"].insertMany(validatedData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            result,
            inserted: validatedData.length,
            data: validatedData
        });
    } catch (error) {
        console.error('Investments API Error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to insert investment data',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
// Helper function to process investment data for dashboard
function processInvestmentData(data) {
    // Filter for USD only
    const usdData = data.filter((item)=>{
        const currency = item["Price CCY"] || item["Currency"] || item.currency;
        return currency === "USD" || currency === "US" || currency === "US$";
    });
    // Group instruments by base name (removing - Commitment/Called suffixes)
    const instrumentGroups = {};
    usdData.forEach((item)=>{
        const instrumentName = item["Instrument"] || item.instrument || "";
        // Use Value WA Portfolio CCY for USD converted amounts, fallback to Quantity
        let amount = parseFloat(item["Value WA Portfolio CCY"] || item["Quantity"] || 0);
        const assetClass = item["Asset Class"] || item.assetClass || "";
        // Skip cash accounts for commitment/called analysis
        if (instrumentName.includes("CASH ACCOUNT")) {
            return;
        }
        // Determine if this is a commitment or called entry
        let baseName = instrumentName;
        let entryType = "";
        if (instrumentName.includes(" - Commitment") || instrumentName.includes("Commitment")) {
            baseName = instrumentName.replace(/ - Commitment/g, "").replace(/Commitment/g, "").trim();
            entryType = "commitment";
        } else if (instrumentName.includes(" - Called") || instrumentName.includes("Called")) {
            baseName = instrumentName.replace(/ - Called/g, "").replace(/Called/g, "").trim();
            entryType = "called";
        } else {
            // For instruments without explicit Commitment/Called in name,
            // check if Capital Commitment or Paid Commitment fields have values
            const capitalCommitment = parseFloat(item["Capital Commitment"] || 0);
            const paidCommitment = parseFloat(item["Paid Commitment"] || 0);
            if (capitalCommitment > 0) {
                entryType = "commitment";
                // Override amount with Capital Commitment if available
                amount = capitalCommitment;
            } else if (paidCommitment > 0) {
                entryType = "called";
                // Override amount with Paid Commitment if available
                amount = paidCommitment;
            }
        }
        if (!instrumentGroups[baseName]) {
            instrumentGroups[baseName] = {
                baseName,
                assetClass
            };
        }
        if (entryType === "commitment") {
            instrumentGroups[baseName].commitment = {
                ...item,
                amount
            };
        } else if (entryType === "called") {
            instrumentGroups[baseName].called = {
                ...item,
                amount
            };
        }
    });
    // Process paired and unpaired instruments
    const pairedInstruments = [];
    const anomalies = [];
    let totalOutstanding = 0;
    let totalCommitment = 0;
    let totalCalled = 0;
    const assetClasses = new Set();
    const yearlyBreakdown = {};
    const statusBreakdown = {};
    Object.values(instrumentGroups).forEach((group)=>{
        const hasCommitment = !!group.commitment;
        const hasCalled = !!group.called;
        if (hasCommitment && hasCalled) {
            // Paired instrument
            const commitmentAmount = group.commitment.amount;
            const calledAmount = group.called.amount;
            const outstanding = commitmentAmount - calledAmount;
            pairedInstruments.push({
                name: group.baseName,
                assetClass: group.assetClass,
                commitment: commitmentAmount,
                called: calledAmount,
                outstanding: outstanding
            });
            totalCommitment += commitmentAmount;
            totalCalled += calledAmount;
            totalOutstanding += outstanding;
            assetClasses.add(group.assetClass);
            // Extract year from statement date if available
            const statementDate = group.commitment["Statement Date"] || group.called["Statement Date"];
            if (statementDate) {
                const year = parseInt(statementDate.split("/")[2]) || new Date().getFullYear();
                yearlyBreakdown[year] = (yearlyBreakdown[year] || 0) + outstanding;
            }
            statusBreakdown["Paired"] = (statusBreakdown["Paired"] || 0) + 1;
        } else {
            // Unpaired instrument - anomaly
            const entry = hasCommitment ? group.commitment : group.called;
            const type = hasCommitment ? "Commitment" : "Called";
            if (entry) {
                anomalies.push({
                    name: group.baseName,
                    assetClass: group.assetClass,
                    type: `Unpaired ${type}`,
                    amount: entry.amount || 0,
                    details: `Missing ${hasCommitment ? 'Called' : 'Commitment'} entry`
                });
                statusBreakdown["Unpaired"] = (statusBreakdown["Unpaired"] || 0) + 1;
            }
        }
    });
    return {
        raw: data,
        summary: {
            totalOutstanding,
            totalCommitment,
            totalCalled,
            assetClasses: Array.from(assetClasses),
            yearlyBreakdown,
            statusBreakdown,
            pairedInstruments,
            anomalies,
            totalInstruments: usdData.length,
            pairedCount: pairedInstruments.length,
            anomalyCount: anomalies.length
        }
    };
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f54bb0bc._.js.map