import mongoose from 'mongoose';

if (!process.env.MONGODB_URI) {
  throw new Error('Invalid/Missing environment variable: "MONGODB_URI"');
}

const MONGODB_URI = process.env.MONGODB_URI;
console.log('MongoDB URI loaded:', MONGODB_URI ? 'URI found' : 'URI missing');

interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

// Cache the database connection in development to prevent multiple connections
let cached: MongooseCache = (global as any).mongoose;

if (!cached) {
  cached = (global as any).mongoose = { conn: null, promise: null };
}

async function connectDB(): Promise<typeof mongoose> {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      serverSelectionTimeoutMS: 10000, // 10 seconds timeout
      socketTimeoutMS: 45000, // 45 seconds socket timeout
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 5, // Maintain a minimum of 5 socket connections
    };

    cached.promise = mongoose.connect(MONGODB_URI, opts);
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

export default connectDB;

// Connection status helper
export async function testConnection(): Promise<boolean> {
  try {
    await connectDB();
    return mongoose.connection.readyState === 1;
  } catch (error) {
    console.error('MongoDB connection failed:', error);
    return false;
  }
}

// Get connection info
export async function getConnectionInfo() {
  try {
    await connectDB();
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();

    return {
      connected: mongoose.connection.readyState === 1,
      database: mongoose.connection.name,
      collections: collections.map(col => col.name),
      host: mongoose.connection.host,
      port: mongoose.connection.port
    };
  } catch (error) {
    console.error('Error getting connection info:', error);
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// MDE Schema Definition - flexible schema to match actual data
const mdeSchema = new mongoose.Schema({}, {
  strict: false,  // Allow any fields
  collection: 'MDE'
});

// Export the MDE model directly
export const MDE = mongoose.models.MDE || mongoose.model('MDE', mdeSchema);

// Keep Investment as alias for backward compatibility, but point to MDE
export const Investment = MDE;

// Generic CRUD operations using Mongoose
export class MongoDBService {
  static async create(): Promise<MongoDBService> {
    await connectDB();
    return new MongoDBService();
  }

  async findAll<T>(modelName: string, filter = {}): Promise<T[]> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.find(filter).lean();
  }

  async findOne<T>(modelName: string, filter: any): Promise<T | null> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.findOne(filter).lean();
  }

  async insertOne<T>(modelName: string, document: T): Promise<any> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    const instance = new Model(document);
    return await instance.save();
  }

  async insertMany<T>(modelName: string, documents: T[]): Promise<any> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.insertMany(documents);
  }

  async updateOne<T>(modelName: string, filter: any, update: any): Promise<any> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.updateOne(filter, { $set: { ...update, updatedAt: new Date() } });
  }

  async deleteOne<T>(modelName: string, filter: any): Promise<any> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.deleteOne(filter);
  }

  async deleteMany<T>(modelName: string, filter: any): Promise<any> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.deleteMany(filter);
  }

  async count(modelName: string, filter = {}): Promise<number> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.countDocuments(filter);
  }

  async aggregate<T>(modelName: string, pipeline: any[]): Promise<T[]> {
    await connectDB();
    const Model = mongoose.models[modelName];
    if (!Model) {
      throw new Error(`Model ${modelName} not found`);
    }
    return await Model.aggregate(pipeline);
  }
}
