export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  content: any;
  uploadedAt: Date;
  type: 'json';
}

export class FileManager {
  private static readonly STORAGE_KEY = 'uploaded-files';

  static saveFiles(files: UploadedFile[]): void {
    try {
      const serializedFiles = files.map(file => ({
        ...file,
        uploadedAt: file.uploadedAt.toISOString(),
      }));
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(serializedFiles));
    } catch (error) {
      console.error('Error saving files to localStorage:', error);
    }
  }

  static loadFiles(): UploadedFile[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];
      
      const parsed = JSON.parse(stored);
      return parsed.map((file: any) => ({
        ...file,
        uploadedAt: new Date(file.uploadedAt),
      }));
    } catch (error) {
      console.error('Error loading files from localStorage:', error);
      return [];
    }
  }

  static addFile(file: UploadedFile): UploadedFile[] {
    const existingFiles = this.loadFiles();
    const updatedFiles = [...existingFiles, file];
    this.saveFiles(updatedFiles);
    return updatedFiles;
  }

  static removeFile(fileId: string): UploadedFile[] {
    const existingFiles = this.loadFiles();
    const updatedFiles = existingFiles.filter(file => file.id !== fileId);
    this.saveFiles(updatedFiles);
    return updatedFiles;
  }

  static clearAllFiles(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  static getFileById(fileId: string): UploadedFile | null {
    const files = this.loadFiles();
    return files.find(file => file.id === fileId) || null;
  }

  static getFilesByType(type: string): UploadedFile[] {
    const files = this.loadFiles();
    return files.filter(file => file.type === type);
  }
}

// Hook for React components
export function useFileManager() {
  const saveFiles = (files: UploadedFile[]) => FileManager.saveFiles(files);
  const loadFiles = () => FileManager.loadFiles();
  const addFile = (file: UploadedFile) => FileManager.addFile(file);
  const removeFile = (fileId: string) => FileManager.removeFile(fileId);
  const clearAllFiles = () => FileManager.clearAllFiles();
  const getFileById = (fileId: string) => FileManager.getFileById(fileId);
  const getFilesByType = (type: string) => FileManager.getFilesByType(type);

  return {
    saveFiles,
    loadFiles,
    addFile,
    removeFile,
    clearAllFiles,
    getFileById,
    getFilesByType,
  };
}
