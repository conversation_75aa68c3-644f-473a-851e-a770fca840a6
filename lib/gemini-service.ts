import { GoogleGenerativeAI } from '@google/generative-ai';
import { UploadedFile } from './file-manager';

export interface ChatMessage {
  role: 'user' | 'model';
  parts: { text: string }[];
}

export interface GeminiResponse {
  success: boolean;
  message?: string;
  error?: string;
  errorType?: 'API_ERROR' | 'NETWORK_ERROR' | 'RATE_LIMIT' | 'INVALID_INPUT' | 'UNKNOWN';
}

class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  private initialize() {
    try {
      const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY;

      if (!apiKey) {
        console.error('Gemini API key not found in environment variables');
        return;
      }

      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash-latest' });
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Gemini service:', error);
      this.isInitialized = false;
    }
  }

  async sendMessage(
    message: string,
    uploadedFiles?: UploadedFile[],
    chatHistory?: ChatMessage[],
    retryCount: number = 0
  ): Promise<GeminiResponse> {
    if (!this.isInitialized || !this.model) {
      return {
        success: false,
        error: 'Gemini service is not properly initialized. Please check your API key.',
        errorType: 'API_ERROR'
      };
    }

    try {
      // Prepare the prompt with context
      let prompt = message;

      // Add file context if files are uploaded
      if (uploadedFiles && uploadedFiles.length > 0) {
        const fileContext = uploadedFiles.map(file => {
          return `File: ${file.name}\nContent: ${JSON.stringify(file.content, null, 2)}`;
        }).join('\n\n');

        prompt = `Context: I have uploaded the following JSON files for analysis:\n\n${fileContext}\n\nUser Question: ${message}`;
      }

      // Create chat session if history exists
      let result;
      if (chatHistory && chatHistory.length > 0) {
        const chat = this.model.startChat({
          history: chatHistory,
        });
        result = await chat.sendMessage(prompt);
      } else {
        result = await this.model.generateContent({
          contents: [{
            role: 'user',
            parts: [{ text: prompt }]
          }]
        });
      }

      const response = await result.response;
      const text = response.text();

      return {
        success: true,
        message: text
      };

    } catch (error: any) {
      console.error('Gemini API error:', error);

      // Handle 503 Service Unavailable / Model Overloaded with retry logic
      if (error?.message?.includes('overloaded') || error?.message?.includes('503') ||
          (error?.status === 503) || error?.code === 503) {

        if (retryCount < 3) { // Retry up to 3 times
          const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
          console.log(`Model overloaded, retrying in ${delay}ms... (attempt ${retryCount + 1}/3)`);

          await new Promise(resolve => setTimeout(resolve, delay));
          return this.sendMessage(message, uploadedFiles, chatHistory, retryCount + 1);
        } else {
          return {
            success: false,
            error: 'The Gemini model is currently overloaded. Please try again in a few minutes. If the problem persists, consider using a different time when there\'s less traffic.',
            errorType: 'RATE_LIMIT'
          };
        }
      }

      // Handle different types of errors
      if (error?.message?.includes('API key')) {
        return {
          success: false,
          error: 'Invalid API key. Please check your Gemini API configuration.',
          errorType: 'API_ERROR'
        };
      }

      if (error?.message?.includes('not found') || error?.message?.includes('404')) {
        return {
          success: false,
          error: `Model not found. Error: ${error.message}`,
          errorType: 'API_ERROR'
        };
      }

      if (error?.message?.includes('quota') || error?.message?.includes('rate limit')) {
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again in a few moments.',
          errorType: 'RATE_LIMIT'
        };
      }

      if (error?.message?.includes('network') || error?.code === 'NETWORK_ERROR') {
        return {
          success: false,
          error: 'Network error. Please check your internet connection and try again.',
          errorType: 'NETWORK_ERROR'
        };
      }

      if (error?.message?.includes('safety') || error?.message?.includes('blocked')) {
        return {
          success: false,
          error: 'Content was blocked by safety filters. Please rephrase your message.',
          errorType: 'INVALID_INPUT'
        };
      }

      // Generic error handling
      return {
        success: false,
        error: error?.message || 'An unexpected error occurred. Please try again.',
        errorType: 'UNKNOWN'
      };
    }
  }

  async analyzeData(data: any, question?: string): Promise<GeminiResponse> {
    const analysisPrompt = question
      ? `Please analyze the following data and answer this question: "${question}"\n\nData: ${JSON.stringify(data, null, 2)}`
      : `Please provide a comprehensive analysis of the following data:\n\n${JSON.stringify(data, null, 2)}`;

    return this.sendMessage(analysisPrompt, undefined, undefined, 0);
  }

  async listAvailableModels(): Promise<string[]> {
    if (!this.genAI) {
      return [];
    }

    try {
      const models = await this.genAI.listModels();
      return models.map(model => model.name);
    } catch (error) {
      console.error('Error listing models:', error);
      return [];
    }
  }

  isReady(): boolean {
    return this.isInitialized;
  }

  getStatus(): { ready: boolean; error?: string } {
    if (this.isInitialized) {
      return { ready: true };
    }

    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return { 
        ready: false, 
        error: 'API key not configured' 
      };
    }

    return { 
      ready: false, 
      error: 'Service initialization failed' 
    };
  }
}

// Export singleton instance
export const geminiService = new GeminiService();
export default geminiService;
