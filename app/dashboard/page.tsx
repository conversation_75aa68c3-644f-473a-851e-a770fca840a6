import { AppSidebar } from "@/components/app-sidebar";
import { DashboardWithMongoDB } from "@/components/dashboard-with-mongodb";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

export default function Page() {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <DashboardWithMongoDB />
      </SidebarInset>
    </SidebarProvider>
  );
}
