import Link from "next/link";

import { Layout } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <Link href="/dashboard">
            <Button>
              <Layout className="size-4" />
              Go to dashboard
            </Button>
          </Link>
        </div>
      </main>
    </div>
  );
}
