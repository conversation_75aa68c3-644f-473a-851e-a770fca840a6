import { AppSidebar } from "@/components/app-sidebar";
import { RealEstateDashboard } from "@/components/real-estate-dashboard";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

export default function RealEstatePage() {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <RealEstateDashboard />
      </SidebarInset>
    </SidebarProvider>
  );
}
