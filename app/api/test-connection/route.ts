import { NextResponse } from 'next/server';
import { testConnection, getConnectionInfo, Investment } from '@/lib/mongodb';
import connectDB from '@/lib/mongodb';

export async function GET() {
  try {
    // Ensure database connection
    await connectDB();

    // Test basic connection
    const isConnected = await testConnection();

    if (!isConnected) {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to MongoDB',
        connected: false
      }, { status: 500 });
    }

    // Get database info
    const connectionInfo = await getConnectionInfo();

    // Get MDE collection data using Mongoose model
    const investmentCount = await Investment.countDocuments();
    const sampleData = await Investment.find({}).limit(3).lean();

    return NextResponse.json({
      success: true,
      connected: true,
      ...connectionInfo,
      investmentCount,
      sampleData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Connection test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Connection test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      connected: false
    }, { status: 500 });
  }
}
