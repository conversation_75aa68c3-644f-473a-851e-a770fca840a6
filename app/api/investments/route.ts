import { NextRequest, NextResponse } from 'next/server';
import { MongoDBService, Investment } from '@/lib/mongodb';
import connectDB from '@/lib/mongodb';

// Define the investment data interface
interface InvestmentData {
  _id?: string;
  instrument: string;
  assetClass: string;
  outstanding?: number;
  commitment?: number;
  called?: number;
  status?: string;
  year?: number;
  [key: string]: any;
}

export async function GET(request: NextRequest) {
  try {
    // Ensure database connection
    await connectDB();

    const { searchParams } = new URL(request.url);
    const assetClass = searchParams.get('assetClass');
    const status = searchParams.get('status');
    const year = searchParams.get('year');

    // Build filter based on query parameters
    const filter: any = {};
    if (assetClass && assetClass !== 'all') {
      filter.assetClass = assetClass;
    }
    if (status) {
      filter.status = status;
    }
    if (year) {
      filter.year = parseInt(year);
    }

    const data = await Investment.find(filter).lean();
    
    // Process data for dashboard consumption
    const processedData = processInvestmentData(data);

    return NextResponse.json({
      success: true,
      data: processedData.raw,
      processed: processedData.summary,
      total: data.length,
      filter
    });

  } catch (error) {
    console.error('Investments API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch investment data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Ensure database connection
    await connectDB();

    const body = await request.json();
    const { data, replace = false } = body;

    if (!data || !Array.isArray(data)) {
      return NextResponse.json(
        { success: false, error: 'Data array is required' },
        { status: 400 }
      );
    }

    // If replace is true, clear existing data first
    if (replace) {
      await Investment.deleteMany({});
    }

    // Validate and process the data
    const validatedData = data.map((item: any) => ({
      instrument: item.instrument || item.name || 'Unknown',
      assetClass: item.assetClass || item.asset_class || 'Unknown',
      outstanding: parseFloat(item.outstanding || item.amount || 0),
      commitment: parseFloat(item.commitment || 0),
      called: parseFloat(item.called || 0),
      status: item.status || 'Active',
      year: item.year || new Date().getFullYear(),
      ...item
    }));

    const result = await Investment.insertMany(validatedData);

    return NextResponse.json({
      success: true,
      result,
      inserted: validatedData.length,
      data: validatedData
    });

  } catch (error) {
    console.error('Investments API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to insert investment data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to process investment data for dashboard
function processInvestmentData(data: any[]) {
  // Filter for USD only
  const usdData = data.filter(item => {
    const currency = item["Price CCY"] || item["Currency"] || item.currency;
    return currency === "USD" || currency === "US" || currency === "US$";
  });

  // Group instruments by base name (removing - Commitment/Called suffixes)
  const instrumentGroups: Record<string, {
    commitment?: any,
    called?: any,
    baseName: string,
    assetClass: string
  }> = {};

  usdData.forEach(item => {
    const instrumentName = item["Instrument"] || item.instrument || "";
    // Use Value WA Portfolio CCY for USD converted amounts, fallback to Quantity
    let amount = parseFloat(item["Value WA Portfolio CCY"] || item["Quantity"] || 0);
    const assetClass = item["Asset Class"] || item.assetClass || "";

    // Skip cash accounts for commitment/called analysis
    if (instrumentName.includes("CASH ACCOUNT")) {
      return;
    }

    // Determine if this is a commitment or called entry
    let baseName = instrumentName;
    let entryType = "";

    if (instrumentName.includes(" - Commitment") || instrumentName.includes("Commitment")) {
      baseName = instrumentName.replace(/ - Commitment/g, "").replace(/Commitment/g, "").trim();
      entryType = "commitment";
    } else if (instrumentName.includes(" - Called") || instrumentName.includes("Called")) {
      baseName = instrumentName.replace(/ - Called/g, "").replace(/Called/g, "").trim();
      entryType = "called";
    } else {
      // For instruments without explicit Commitment/Called in name,
      // check if Capital Commitment or Paid Commitment fields have values
      const capitalCommitment = parseFloat(item["Capital Commitment"] || 0);
      const paidCommitment = parseFloat(item["Paid Commitment"] || 0);

      if (capitalCommitment > 0) {
        entryType = "commitment";
        // Override amount with Capital Commitment if available
        amount = capitalCommitment;
      } else if (paidCommitment > 0) {
        entryType = "called";
        // Override amount with Paid Commitment if available
        amount = paidCommitment;
      }
    }

    if (!instrumentGroups[baseName]) {
      instrumentGroups[baseName] = {
        baseName,
        assetClass
      };
    }

    if (entryType === "commitment") {
      instrumentGroups[baseName].commitment = { ...item, amount };
    } else if (entryType === "called") {
      instrumentGroups[baseName].called = { ...item, amount };
    }
  });

  // Process paired and unpaired instruments
  const pairedInstruments: any[] = [];
  const anomalies: any[] = [];
  let totalOutstanding = 0;
  let totalCommitment = 0;
  let totalCalled = 0;
  const assetClasses = new Set<string>();
  const yearlyBreakdown: Record<number, number> = {};
  const statusBreakdown: Record<string, number> = {};

  Object.values(instrumentGroups).forEach(group => {
    const hasCommitment = !!group.commitment;
    const hasCalled = !!group.called;

    if (hasCommitment && hasCalled) {
      // Paired instrument
      const commitmentAmount = group.commitment.amount;
      const calledAmount = group.called.amount;
      const outstanding = commitmentAmount - calledAmount;

      pairedInstruments.push({
        name: group.baseName,
        assetClass: group.assetClass,
        commitment: commitmentAmount,
        called: calledAmount,
        outstanding: outstanding
      });

      totalCommitment += commitmentAmount;
      totalCalled += calledAmount;
      totalOutstanding += outstanding;

      assetClasses.add(group.assetClass);

      // Extract year from statement date if available
      const statementDate = group.commitment["Statement Date"] || group.called["Statement Date"];
      if (statementDate) {
        const year = parseInt(statementDate.split("/")[2]) || new Date().getFullYear();
        yearlyBreakdown[year] = (yearlyBreakdown[year] || 0) + outstanding;
      }

      statusBreakdown["Paired"] = (statusBreakdown["Paired"] || 0) + 1;

    } else {
      // Unpaired instrument - anomaly
      const entry = hasCommitment ? group.commitment : group.called;
      const type = hasCommitment ? "Commitment" : "Called";

      if (entry) {
        anomalies.push({
          name: group.baseName,
          assetClass: group.assetClass,
          type: `Unpaired ${type}`,
          amount: entry.amount || 0,
          details: `Missing ${hasCommitment ? 'Called' : 'Commitment'} entry`
        });

        statusBreakdown["Unpaired"] = (statusBreakdown["Unpaired"] || 0) + 1;
      }
    }
  });

  return {
    raw: data,
    summary: {
      totalOutstanding,
      totalCommitment,
      totalCalled,
      assetClasses: Array.from(assetClasses),
      yearlyBreakdown,
      statusBreakdown,
      pairedInstruments,
      anomalies,
      totalInstruments: usdData.length,
      pairedCount: pairedInstruments.length,
      anomalyCount: anomalies.length
    }
  };
}
