import { NextRequest, NextResponse } from 'next/server';
import { MongoDBService, Investment } from '@/lib/mongodb';
import connectDB from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    // Ensure database connection
    await connectDB();

    const { searchParams } = new URL(request.url);
    const collection = searchParams.get('collection') || 'MDE';
    const limit = parseInt(searchParams.get('limit') || '100');
    const skip = parseInt(searchParams.get('skip') || '0');

    // For now, we'll default to MDE collection
    // You can extend this to support other collections later
    let data;
    if (collection === 'MDE' || collection === 'investments') {
      data = await Investment.find({}).lean();
    } else {
      // Generic collection access (you can add more models as needed)
      const mongoService = await MongoDBService.create();
      data = await mongoService.findAll(collection);
    }
    
    // Apply pagination if needed
    const paginatedData = data.slice(skip, skip + limit);
    
    return NextResponse.json({
      success: true,
      data: paginatedData,
      total: data.length,
      collection,
      pagination: {
        limit,
        skip,
        hasMore: skip + limit < data.length
      }
    });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch data from MongoDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Ensure database connection
    await connectDB();

    const body = await request.json();
    const { collection = 'MDE', data } = body;

    if (!data) {
      return NextResponse.json(
        { success: false, error: 'Data is required' },
        { status: 400 }
      );
    }

    let result;
    if (collection === 'MDE' || collection === 'investments') {
      if (Array.isArray(data)) {
        result = await Investment.insertMany(data);
      } else {
        const investment = new Investment(data);
        result = await investment.save();
      }
    } else {
      // Generic collection access
      const mongoService = await MongoDBService.create();
      if (Array.isArray(data)) {
        result = await mongoService.insertMany(collection, data);
      } else {
        result = await mongoService.insertOne(collection, data);
      }
    }

    return NextResponse.json({
      success: true,
      result,
      inserted: Array.isArray(data) ? data.length : 1
    });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to insert data into MongoDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
